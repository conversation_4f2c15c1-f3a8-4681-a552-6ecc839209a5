import { describe, it, expect, vi } from 'vitest'
import { NextRequest } from 'next/server'

vi.unmock('@/lib/csrf')

import { middleware } from '@/middleware'
import { generateCSRFToken } from '@/lib/csrf'

describe('CSRF grace-window cookie-only acceptance', () => {
  it('accepts cookie-only within 5s grace when header invalid', async () => {
    const fresh = await generateCSRFToken()
    const req = new NextRequest('http://localhost:3000/api/x', {
      method: 'POST',
      headers: {
        'x-csrf-token': 'bad',
        cookie: `csrf-token=${fresh}`,
      },
    })

    const res = await middleware(req as any)
    expect((res as any).status).not.toBe(403)
  })
})
