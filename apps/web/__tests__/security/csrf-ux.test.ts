import { describe, it, expect, vi } from 'vitest'
import { NextRequest } from 'next/server'

// Unmock CSRF
vi.unmock('@/lib/csrf')

import { middleware } from '@/middleware'
import { generateCSRFToken } from '@/lib/csrf'

describe('CSRF UX scenarios: GET/POST and multiple tabs', () => {
  it('GET sets X-CSRF-Token so a subsequent POST with header succeeds', async () => {
    const getReq = new NextRequest('http://localhost:3000/')
    const getRes = await middleware(getReq as any)
    const headerToken = (getRes as any).headers.get('X-CSRF-Token')
    expect(headerToken).toBeTruthy()

    const postReq = new NextRequest('http://localhost:3000/api/test', {
      method: 'POST',
      headers: {
        'x-csrf-token': headerToken,
      },
    })

    const postRes = await middleware(postReq as any)
    expect((postRes as any).status).not.toBe(403)
  })

  it('Old tab with stale header succeeds due to fresh cookie-only within grace window', async () => {
    // Simulate fresh cookie from a new GET
    const newGet = new NextRequest('http://localhost:3000/')
    const newGetRes = await middleware(newGet as any)
    const freshToken = (newGetRes as any).headers.get('X-CSRF-Token')

    // Old tab sends invalid header but did receive updated cookie via shared domain
    const postReq = new NextRequest('http://localhost:3000/api/test', {
      method: 'POST',
      headers: {
        'x-csrf-token': 'stale-header',
        cookie: `csrf-token=${freshToken}`,
      },
    })

    const res = await middleware(postReq as any)
    expect((res as any).status).not.toBe(403)
  })
})
