import { describe, it, expect, vi, beforeEach } from 'vitest'
import { NextRequest, NextResponse } from 'next/server'

// Use the REAL CSRF module in this suite (override global setup mock)
vi.mock('@/lib/csrf', async importOriginal => {
  const actual = await importOriginal<typeof import('@/lib/csrf')>()
  return { ...actual }
})

// We'll dynamically import modules inside tests to ensure per-test mocks apply

// Helpers
async function makeTokenWithAge(ageMs: number): Promise<string> {
  // Create a token then manually rewrite its timestamp to simulate age
  const { generateCSRFToken } = await import('@/lib/csrf')
  const token = await generateCSRFToken()
  const decoded = atob(token)
  const [data, sig] = decoded.split('.')
  const tsStr = data.slice(-13)
  const newTs = (Date.now() - ageMs).toString()
  const newData = data.slice(0, -13) + newTs
  // Re-sign is not trivial; instead, return original (valid) token for this test path where we only need relative behavior
  // For explicit expiry simulation, we validate with a very small maxAge in later tests
  return btoa(newData + '.' + sig)
}

describe('CSRF token rotation and grace window', () => {
  beforeEach(() => {
    vi.resetModules()
  })

  it('allows request when header is invalid but cookie is valid within grace window', async () => {
    const { generateCSRFToken } = await import('@/lib/csrf')
    const { middleware } = await import('@/middleware')
    const validCookie = await generateCSRFToken()
    const req = new NextRequest('http://localhost:3000/api/test', {
      method: 'POST',
      headers: {
        'x-csrf-token': 'invalid-header',
        cookie: `csrf-token=${validCookie}`,
      },
    })

    const res = await middleware(req as any)
    // Expect not a 403
    expect((res as any).status).not.toBe(403)
  })

  it('rejects request when both header and cookie are invalid (outside grace)', async () => {
    // Re-import middleware after overriding CSRF validate to ensure it returns false
    vi.resetModules()
    const actual = await import('@/lib/csrf')
    vi.doMock('@/lib/csrf', () => ({
      ...actual,
      validateCSRFFromRequest: vi.fn().mockResolvedValue(false),
    }))
    const { middleware } = await import('@/middleware')

    const req = new NextRequest('http://localhost:3000/api/test', {
      method: 'POST',
      headers: {
        'x-csrf-token': 'invalid-header-token',
      },
    })

    const res = await middleware(req as any)
    expect((res as any).status).toBe(403)
  })

  it('rotates/sets CSRF token header for GET and keeps it for client', async () => {
    const { middleware } = await import('@/middleware')
    const req = new NextRequest('http://localhost:3000/')
    const res = await middleware(req as any)
    // Header should be set
    expect((res as any).headers.get('X-CSRF-Token')).toBeTruthy()
  })

  it('can validate an expired token if maxAge is extended (align with 24h)', async () => {
    const actual =
      await vi.importActual<typeof import('@/lib/csrf')>('@/lib/csrf')
    const token = await actual.generateCSRFToken()
    const validNow = await actual.validateCSRFToken(token)
    expect(validNow).toBe(true)
    // Simulate expiration by checking with a very short maxAge and slight delay
    await new Promise(r => setTimeout(r, 2))
    const validShort = await actual.validateCSRFToken(token, undefined, 1)
    expect(validShort).toBe(false)
  })
})
