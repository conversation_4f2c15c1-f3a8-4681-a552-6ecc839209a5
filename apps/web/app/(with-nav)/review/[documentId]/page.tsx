'use client'
/* eslint-disable @next/next/no-img-element */

import React, { useState, useEffect, useCallback } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { createSecureBrowserClient } from '@/lib/session-security'
import { useOrgEntitySelection } from '@/hooks/useOrgEntitySelection'
import { useAuth } from '@/contexts/AuthContext'
import { ZenIcons } from '@/components/ZenIcons'
import type {
  InboxDocument,
  DocumentReviewFormData,
  DocumentReviewFormErrors,
  ExtractionResult,
} from '@/types/document-review'
import { normalizeSuggestion } from '@/lib/suggestion'
import ChangeConfirmationModal from '@/components/ChangeConfirmationModal'
import {
  parseJsonSafe,
  getApiErrorMessage,
  isApiSuccessWithData,
  extractData,
} from '@/lib/http/api'
import { fetchAccountsMap, getAccountDisplayName } from '@/lib/accounts'

type RegionFieldKey = 'supplier' | 'customer' | 'payment' | 'totals' | 'invoice'
type CandidateEntry = {
  region_id: string
  label: string
  score: number
  evidence: string[]
  data: Record<string, string>
}

const REGION_COLORS: Record<string, string> = {
  SUPPLIER: 'rgba(59, 130, 246, 0.25)',
  CUSTOMER: 'rgba(16, 185, 129, 0.25)',
  PAYMENT: 'rgba(217, 70, 239, 0.25)',
  TOTALS: 'rgba(234, 179, 8, 0.25)',
  META: 'rgba(29, 78, 216, 0.2)',
  LINE_ITEMS: 'rgba(107, 114, 128, 0.18)',
  OTHER: 'rgba(148, 163, 184, 0.15)',
}

const LABEL_FIELD_MAP: Partial<Record<string, RegionFieldKey>> = {
  SUPPLIER: 'supplier',
  CUSTOMER: 'customer',
  PAYMENT: 'payment',
  TOTALS: 'totals',
  META: 'invoice',
}

// Zen UI Theme
const zenTheme = {
  bg: '#FBFAF5',
  surface: '#FFFFFC',
  primaryText: '#1a1a1a',
  secondaryText: '#6b7280',
  subtleText: '#9ca3af',
  border: '#f3f4f6',
  borderHover: '#e5e7eb',
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  primaryAction: '#3b82f6',
  shadow: '0 1px 3px 0 rgba(0, 0, 0, 0.05)',
  shadowHover: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
}

export default function DocumentReviewPage() {
  const router = useRouter()
  const params = useParams()
  const documentId = parseInt(params.documentId as string)
  const supabase = createSecureBrowserClient()
  const { user } = useAuth()
  const { currentEntity } = useOrgEntitySelection()

  // State
  const [document, setDocument] = useState<InboxDocument | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [documentUrl, setDocumentUrl] = useState<string | null>(null)
  const [submitting, setSubmitting] = useState(false)
  const [success, setSuccess] = useState<string | null>(null)

  // Navigation state
  const [reviewableDocuments, setReviewableDocuments] = useState<number[]>([])
  const [currentIndex, setCurrentIndex] = useState(-1)

  // Account lookup state
  const [accounts, setAccounts] = useState<
    Map<number, { code: string; name: string }>
  >(new Map())

  // Form state
  const [formData, setFormData] = useState<DocumentReviewFormData>({
    supplierName: '',
    supplierVat: '',
    supplierAddress: '',
    invoiceNumber: '',
    invoiceIssueDate: '',
    invoiceDueDate: '',
    invoiceCurrency: 'EUR',
    invoiceNet: '',
    invoiceVat: '',
    invoiceGross: '',
    paymentIban: '',
    paymentBic: '',
    paymentStructuredRef: '',
    journalDate: '',
    journalReference: '',
    journalDescription: '',
    lines: [],
    suggestionLines: [],
  })

  const [formErrors, setFormErrors] = useState<DocumentReviewFormErrors>({})
  const [hasChanges, setHasChanges] = useState(false)
  const [originalFormData, setOriginalFormData] =
    useState<DocumentReviewFormData | null>(null)

  // Change confirmation modal state
  const [showChangeConfirmation, setShowChangeConfirmation] = useState(false)
  const [pendingChanges, setPendingChanges] = useState<
    Array<{
      field: string
      label: string
      oldValue: string
      newValue: string
    }>
  >([])
  const [confirmingChanges, setConfirmingChanges] = useState(false)
  const [iframeError, setIframeError] = useState(false)
  const [activeExtraction, setActiveExtraction] =
    useState<ExtractionResult | null>(null)
  const [selectedField, setSelectedField] = useState<RegionFieldKey>('supplier')
  const [highlightRegionId, setHighlightRegionId] = useState<string | null>(
    null
  )

  const getRegionForField = useCallback(
    (extraction: ExtractionResult | null, field: RegionFieldKey) => {
      if (!extraction?.regions?.length) return null
      return (
        extraction.regions.find(region => region.selectedFor.includes(field)) ||
        null
      )
    },
    []
  )

  const handleFieldFocus = useCallback(
    (field: RegionFieldKey) => {
      setSelectedField(field)
      const region = getRegionForField(activeExtraction, field)
      setHighlightRegionId(region?.id ?? null)
    },
    [activeExtraction, getRegionForField]
  )

  useEffect(() => {
    const region = getRegionForField(activeExtraction, selectedField)
    setHighlightRegionId(region?.id ?? null)
  }, [activeExtraction, selectedField, getRegionForField])

  const handleCandidateSelect = useCallback(
    (field: RegionFieldKey, candidate: CandidateEntry) => {
      if (!activeExtraction) return

      const updatedRegions = (activeExtraction.regions || []).map(region => {
        const remaining = region.selectedFor.filter(
          (entry: string) => entry !== field
        )
        if (region.id === candidate.region_id) {
          return { ...region, selectedFor: [...remaining, field] }
        }
        if (remaining.length !== region.selectedFor.length) {
          return { ...region, selectedFor: remaining }
        }
        return region
      })

      const updatedExtraction: ExtractionResult = {
        ...activeExtraction,
        regions: updatedRegions,
      }

      const candidateData = candidate.data || {}

      if (field === 'supplier') {
        updatedExtraction.supplier = {
          ...updatedExtraction.supplier,
          name: candidateData.name ?? updatedExtraction.supplier?.name ?? '',
          vat: candidateData.vat ?? updatedExtraction.supplier?.vat ?? '',
          address:
            candidateData.address ?? updatedExtraction.supplier?.address ?? '',
        }
        setFormData(prev => ({
          ...prev,
          supplierName: candidateData.name ?? prev.supplierName,
          supplierVat: candidateData.vat ?? prev.supplierVat,
          supplierAddress: candidateData.address ?? prev.supplierAddress,
        }))
      }

      if (field === 'customer') {
        updatedExtraction.customer = {
          name: candidateData.name || '',
          vat: candidateData.vat || '',
          address: candidateData.address || '',
        }
      }

      if (field === 'payment') {
        updatedExtraction.paymentInstructions = {
          ...updatedExtraction.paymentInstructions,
          iban:
            candidateData.iban ??
            updatedExtraction.paymentInstructions?.iban ??
            '',
          bic:
            candidateData.bic ??
            updatedExtraction.paymentInstructions?.bic ??
            '',
          structuredRef:
            candidateData.structured_ref ??
            updatedExtraction.paymentInstructions?.structuredRef ??
            '',
        }
        setFormData(prev => ({
          ...prev,
          paymentIban: candidateData.iban ?? prev.paymentIban,
          paymentBic: candidateData.bic ?? prev.paymentBic,
          paymentStructuredRef:
            candidateData.structured_ref ?? prev.paymentStructuredRef,
        }))
      }

      if (field === 'totals') {
        updatedExtraction.totals = {
          ...updatedExtraction.totals,
          net: candidateData.net ?? updatedExtraction.totals?.net ?? '',
          vat: candidateData.vat ?? updatedExtraction.totals?.vat ?? '',
          gross: candidateData.gross ?? updatedExtraction.totals?.gross ?? '',
          currency:
            candidateData.currency ??
            updatedExtraction.totals?.currency ??
            'EUR',
        }
        setFormData(prev => ({
          ...prev,
          invoiceNet: candidateData.net ?? prev.invoiceNet,
          invoiceVat: candidateData.vat ?? prev.invoiceVat,
          invoiceGross: candidateData.gross ?? prev.invoiceGross,
        }))
      }

      if (field === 'invoice') {
        updatedExtraction.invoice = {
          ...updatedExtraction.invoice,
          number:
            candidateData.number ?? updatedExtraction.invoice?.number ?? '',
          issueDate:
            candidateData.issue_date ??
            updatedExtraction.invoice?.issueDate ??
            '',
          dueDate:
            candidateData.due_date ?? updatedExtraction.invoice?.dueDate ?? '',
        }
        setFormData(prev => ({
          ...prev,
          invoiceNumber: candidateData.number ?? prev.invoiceNumber,
          invoiceIssueDate: candidateData.issue_date ?? prev.invoiceIssueDate,
          invoiceDueDate: candidateData.due_date ?? prev.invoiceDueDate,
        }))
      }

      setActiveExtraction(updatedExtraction)
      setHighlightRegionId(candidate.region_id)
      setSelectedField(field)
      setHasChanges(true)
    },
    [activeExtraction, setFormData]
  )

  const handleRegionClick = useCallback(
    (regionId: string) => {
      if (!activeExtraction?.regions) return
      const region = activeExtraction.regions.find(
        entry => entry.id === regionId
      )
      if (!region) return
      setHighlightRegionId(regionId)
      const field = (region.selectedFor[0] ||
        LABEL_FIELD_MAP[region.label]) as RegionFieldKey
      if (field) {
        setSelectedField(field)
      }
    },
    [activeExtraction]
  )

  const renderCandidateChips = useCallback(
    (field: RegionFieldKey, label: string) => {
      let candidates: CandidateEntry[] | undefined
      const all = activeExtraction?.candidates as
        | Partial<Record<RegionFieldKey, CandidateEntry[]>>
        | undefined
      if (all) {
        switch (field) {
          case 'supplier':
            candidates = all.supplier
            break
          case 'customer':
            candidates = all.customer
            break
          case 'payment':
            candidates = all.payment
            break
          case 'totals':
            candidates = all.totals
            break
          case 'invoice':
            candidates = all.invoice
            break
        }
      }
      if (!candidates?.length) return null

      return (
        <div
          style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '8px',
            marginBottom: '12px',
          }}
        >
          <span style={{ fontSize: '12px', color: zenTheme.secondaryText }}>
            {label} candidates:
          </span>
          {candidates.map(candidate => {
            const isActive = highlightRegionId === candidate.region_id
            return (
              <button
                key={`${field}-${candidate.region_id}`}
                onClick={() => handleCandidateSelect(field, candidate)}
                style={{
                  padding: '4px 10px',
                  fontSize: '12px',
                  borderRadius: '12px',
                  border: `1px solid ${isActive ? zenTheme.primaryAction : zenTheme.border}`,
                  backgroundColor: isActive ? '#e0f2ff' : 'transparent',
                  color: isActive
                    ? zenTheme.primaryAction
                    : zenTheme.secondaryText,
                  cursor: 'pointer',
                }}
              >
                {(
                  candidate.data?.name ||
                  candidate.data?.number ||
                  candidate.data?.iban ||
                  candidate.region_id
                ).slice(0, 24)}
              </button>
            )
          })}
        </div>
      )
    },
    [activeExtraction, handleCandidateSelect, highlightRegionId]
  )

  // Fetch reviewable documents for navigation
  const fetchReviewableDocuments = useCallback(async () => {
    if (!user || !currentEntity) return

    try {
      await supabase.auth.getSession()

      // Fetch documents with extracted or suggested status
      const { data, error } = await supabase
        .from('inbox_documents')
        .select('id')
        .eq('entity_id', currentEntity.entity_id!)
        .in('status', ['extracted', 'suggested'])
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching reviewable documents:', error)
        return
      }

      const documentIds = (data || []).map(doc => doc.id)
      setReviewableDocuments(documentIds)

      // Find current document index
      const index = documentIds.indexOf(documentId)
      setCurrentIndex(index)
    } catch (err) {
      console.error('Error fetching reviewable documents:', err)
    }
  }, [user, currentEntity, documentId, supabase])

  // Fetch accounts for account name lookup (shared helper)
  const fetchAccounts = useCallback(async () => {
    if (!user || !currentEntity) return
    try {
      const id = currentEntity.entity_id
      if (typeof id !== 'number') return
      const map = await fetchAccountsMap(id)
      setAccounts(map)
    } catch (err) {
      console.error('Error fetching accounts:', err)
    }
  }, [user, currentEntity])

  // Use shared helper for display

  // Fetch document data
  const fetchDocument = useCallback(async () => {
    if (!documentId || !user) return

    try {
      setLoading(true)
      setError(null)

      // Fetch document details from API
      const response = await fetch(`/api/documents/${documentId}`)

      if (!response.ok) {
        const errorData = await parseJsonSafe(response)
        throw new Error(
          getApiErrorMessage(errorData, 'Failed to fetch document')
        )
      }

      const result = await parseJsonSafe(response)
      const docData = extractData<InboxDocument | null>(result) ?? null

      if (docData) {
        setDocument(docData)
        initializeFormData(docData)
      }

      // Fetch document URL for viewing
      const urlResponse = await fetch(`/api/documents/${documentId}/download`)

      if (urlResponse.ok) {
        type DownloadData = { download_url?: string }
        const urlResult = await parseJsonSafe(urlResponse)
        if (
          isApiSuccessWithData<DownloadData>(urlResult, ['download_url']) &&
          urlResult.success &&
          urlResult.data?.download_url
        ) {
          setDocumentUrl(urlResult.data.download_url)
        }
      }
    } catch (err) {
      console.error('Error fetching document:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch document')
    } finally {
      setLoading(false)
    }
  }, [documentId, user])

  // Initialize form data from document
  const initializeFormData = (doc: InboxDocument) => {
    const extraction = doc.extraction
    if (extraction) {
      setActiveExtraction(extraction)
      const primarySupplierRegion = extraction.regions?.find(region =>
        region.selectedFor.includes('supplier')
      )
      setHighlightRegionId(primarySupplierRegion?.id ?? null)
    } else {
      setActiveExtraction(null)
      setHighlightRegionId(null)
    }
    const suggestion = normalizeSuggestion(doc.suggestion)

    const initialData = {
      // Supplier information from extraction
      supplierName: extraction?.supplier?.name || doc.supplier_name || '',
      supplierVat: extraction?.supplier?.vat || '',
      supplierAddress: extraction?.supplier?.address || '',

      // Invoice information from extraction
      invoiceNumber: extraction?.invoice?.number || doc.invoice_number || '',
      invoiceIssueDate:
        extraction?.invoice?.issueDate || doc.invoice_date || '',
      invoiceDueDate: extraction?.invoice?.dueDate || '',
      invoiceCurrency: 'EUR' as const,
      invoiceNet:
        extraction?.totals?.net ||
        extraction?.invoice?.net ||
        doc.net_amount ||
        '',
      invoiceVat:
        extraction?.totals?.vat ||
        extraction?.invoice?.vat ||
        doc.vat_amount ||
        '',
      invoiceGross:
        extraction?.totals?.gross ||
        extraction?.invoice?.gross ||
        doc.gross_amount ||
        '',

      paymentIban: extraction?.paymentInstructions?.iban || '',
      paymentBic: extraction?.paymentInstructions?.bic || '',
      paymentStructuredRef:
        extraction?.paymentInstructions?.structuredRef || '',

      // Journal information from suggestion
      journalDate:
        suggestion?.journalDate || extraction?.invoice?.issueDate || '',
      journalReference:
        suggestion?.reference || extraction?.invoice?.number || '',
      journalDescription:
        suggestion?.description ||
        `Invoice from ${extraction?.supplier?.name || 'Supplier'}`,

      // Line items from extraction
      lines:
        extraction?.lines?.map(line => ({
          description: line.description,
          quantity: line.quantity,
          unitPrice: line.unitPrice,
          vatRate: line.vatRate,
          accountHint: line.accountHint,
        })) || [],

      // Suggestion lines for review
      suggestionLines:
        suggestion?.lines?.map(line => ({
          accountId: line.accountId,
          debit: line.debit || '0',
          credit: line.credit || '0',
          vatCodeId: line.vatCodeId,
          memo: line.memo,
        })) || [],
    }

    setFormData(initialData)
    setOriginalFormData(initialData)
    setFormErrors({})
    setHasChanges(false)
  }

  useEffect(() => {
    void fetchDocument()
    void fetchReviewableDocuments()
    void fetchAccounts()
  }, [fetchDocument, fetchReviewableDocuments, fetchAccounts])

  // Detect changes between original and current form data
  const detectChanges = useCallback(() => {
    if (!originalFormData) return []

    const changes: Array<{
      field: string
      label: string
      oldValue: string
      newValue: string
    }> = []

    const pairs = [
      [
        'supplierName',
        'Supplier Name',
        originalFormData.supplierName,
        formData.supplierName,
      ],
      [
        'supplierVat',
        'VAT Number',
        originalFormData.supplierVat,
        formData.supplierVat,
      ],
      [
        'supplierAddress',
        'Address',
        originalFormData.supplierAddress,
        formData.supplierAddress,
      ],
      [
        'invoiceNumber',
        'Invoice Number',
        originalFormData.invoiceNumber,
        formData.invoiceNumber,
      ],
      [
        'invoiceIssueDate',
        'Invoice Date',
        originalFormData.invoiceIssueDate,
        formData.invoiceIssueDate,
      ],
      [
        'invoiceDueDate',
        'Due Date',
        originalFormData.invoiceDueDate,
        formData.invoiceDueDate,
      ],
      [
        'invoiceCurrency',
        'Currency',
        originalFormData.invoiceCurrency,
        formData.invoiceCurrency,
      ],
      [
        'invoiceNet',
        'Net Amount',
        originalFormData.invoiceNet,
        formData.invoiceNet,
      ],
      [
        'invoiceVat',
        'VAT Amount',
        originalFormData.invoiceVat,
        formData.invoiceVat,
      ],
      [
        'invoiceGross',
        'Gross Amount',
        originalFormData.invoiceGross,
        formData.invoiceGross,
      ],
      [
        'paymentIban',
        'Payment IBAN',
        originalFormData.paymentIban,
        formData.paymentIban,
      ],
      [
        'paymentBic',
        'Payment BIC',
        originalFormData.paymentBic,
        formData.paymentBic,
      ],
      [
        'paymentStructuredRef',
        'Structured Reference',
        originalFormData.paymentStructuredRef,
        formData.paymentStructuredRef,
      ],
      [
        'journalDate',
        'Journal Date',
        originalFormData.journalDate,
        formData.journalDate,
      ],
      [
        'journalReference',
        'Journal Reference',
        originalFormData.journalReference,
        formData.journalReference,
      ],
      [
        'journalDescription',
        'Journal Description',
        originalFormData.journalDescription,
        formData.journalDescription,
      ],
    ] as const
    for (const [field, label, oldVal, newVal] of pairs) {
      const oldValue = String(oldVal || '')
      const newValue = String(newVal || '')
      if (oldValue !== newValue) {
        changes.push({ field, label, oldValue, newValue })
      }
    }

    return changes
  }, [originalFormData, formData])

  // Handle form field changes
  const handleFieldChange = (
    field: keyof DocumentReviewFormData,
    value: string
  ) => {
    setFormData(prev => {
      const next = { ...prev }
      switch (field) {
        case 'supplierName':
          next.supplierName = value
          break
        case 'supplierVat':
          next.supplierVat = value
          break
        case 'supplierAddress':
          next.supplierAddress = value
          break
        case 'invoiceNumber':
          next.invoiceNumber = value
          break
        case 'invoiceIssueDate':
          next.invoiceIssueDate = value
          break
        case 'invoiceDueDate':
          next.invoiceDueDate = value
          break
        case 'invoiceCurrency':
          next.invoiceCurrency =
            value as DocumentReviewFormData['invoiceCurrency']
          break
        case 'invoiceNet':
          next.invoiceNet = value
          break
        case 'invoiceVat':
          next.invoiceVat = value
          break
        case 'invoiceGross':
          next.invoiceGross = value
          break
        case 'paymentIban':
          next.paymentIban = value
          break
        case 'paymentBic':
          next.paymentBic = value
          break
        case 'paymentStructuredRef':
          next.paymentStructuredRef = value
          break
        case 'journalDate':
          next.journalDate = value
          break
        case 'journalReference':
          next.journalReference = value
          break
        case 'journalDescription':
          next.journalDescription = value
          break
        case 'lines':
        case 'suggestionLines':
          return prev
      }
      return next
    })
    setHasChanges(true)

    // Clear field-specific errors without dynamic key access
    setFormErrors(prev => {
      const next = { ...prev }
      switch (field) {
        case 'supplierName':
          next.supplierName = undefined
          break
        case 'supplierVat':
          next.supplierVat = undefined
          break
        case 'supplierAddress':
          next.supplierAddress = undefined
          break
        case 'invoiceNumber':
          next.invoiceNumber = undefined
          break
        case 'invoiceIssueDate':
          next.invoiceIssueDate = undefined
          break
        case 'invoiceDueDate':
          next.invoiceDueDate = undefined
          break
        case 'invoiceCurrency':
          next.invoiceCurrency = undefined
          break
        case 'invoiceNet':
          next.invoiceNet = undefined
          break
        case 'invoiceVat':
          next.invoiceVat = undefined
          break
        case 'invoiceGross':
          next.invoiceGross = undefined
          break
        case 'paymentIban':
          next.paymentIban = undefined
          break
        case 'paymentBic':
          next.paymentBic = undefined
          break
        case 'paymentStructuredRef':
          next.paymentStructuredRef = undefined
          break
        case 'journalDate':
          next.journalDate = undefined
          break
        case 'journalReference':
          next.journalReference = undefined
          break
        case 'journalDescription':
          next.journalDescription = undefined
          break
        default:
          return prev
      }
      return next
    })
  }

  // Navigation functions
  const handleBack = () => {
    void router.push('/inbox')
  }

  const handleNext = useCallback(() => {
    if (currentIndex >= 0 && currentIndex < reviewableDocuments.length - 1) {
      const nextDocumentId = reviewableDocuments[currentIndex + 1]
      void router.push(`/review/${nextDocumentId}`)
    }
  }, [currentIndex, reviewableDocuments, router])

  const handlePrevious = useCallback(() => {
    if (currentIndex > 0) {
      const previousDocumentId = reviewableDocuments[currentIndex - 1]
      void router.push(`/review/${previousDocumentId}`)
    }
  }, [currentIndex, reviewableDocuments, router])

  // Keyboard navigation effect moved below to satisfy TypeScript ordering

  // Actually confirm the document
  const confirmDocument = useCallback(async () => {
    if (!document) return

    try {
      setSubmitting(true)
      setConfirmingChanges(true)
      setError(null)

      // Build corrections object if there are changes
      let corrections: Partial<ExtractionResult> | undefined
      if (hasChanges) {
        corrections = {
          supplier: {
            name: formData.supplierName,
            vat: formData.supplierVat,
            address: formData.supplierAddress,
          },
          invoice: {
            number: formData.invoiceNumber,
            issueDate: formData.invoiceIssueDate,
            dueDate: formData.invoiceDueDate,
            currency: 'EUR',
            net: formData.invoiceNet,
            vat: formData.invoiceVat,
            gross: formData.invoiceGross,
          },
          totals: {
            net: formData.invoiceNet,
            vat: formData.invoiceVat,
            gross: formData.invoiceGross,
            currency: 'EUR',
          },
          paymentInstructions: {
            iban: formData.paymentIban,
            bic: formData.paymentBic,
            structuredRef: formData.paymentStructuredRef,
          },
          lines: formData.lines.map(line => ({
            description: line.description,
            quantity: line.quantity,
            unitPrice: line.unitPrice,
            vatRate: line.vatRate,
            accountHint: line.accountHint,
          })),
        }
      }

      const requestBody: {
        document_id: number
        correction?: Partial<ExtractionResult>
      } = { document_id: documentId }
      if (corrections) {
        requestBody.correction = corrections
      }

      const response = await fetch(`/api/documents/${documentId}/confirm`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      })

      if (!response.ok) {
        const errorData = await parseJsonSafe(response)
        throw new Error(
          getApiErrorMessage(errorData, 'Failed to confirm document')
        )
      }

      const result = await parseJsonSafe(response)
      let status: string | undefined
      const data = extractData<unknown>(result)
      if (
        typeof data === 'object' &&
        data &&
        'status' in (data as Record<string, unknown>)
      ) {
        const s = (data as Record<string, unknown>).status
        if (typeof s === 'string') status = s
      }
      setSuccess(
        `Document confirmed successfully! Status: ${status || 'processed'}`
      )

      // Navigate back to inbox after a short delay
      setTimeout(() => {
        router.push('/inbox')
      }, 2000)
    } catch (err) {
      console.error('Error confirming document:', err)
      setError(
        err instanceof Error ? err.message : 'Failed to confirm document'
      )
    } finally {
      setSubmitting(false)
      setConfirmingChanges(false)
      setShowChangeConfirmation(false)
    }
  }, [
    document,
    hasChanges,
    formData,
    documentId,
    router,
    setSubmitting,
    setConfirmingChanges,
    setError,
    setSuccess,
    setShowChangeConfirmation,
  ])

  // Show change confirmation if there are changes
  const handleConfirm = useCallback(async () => {
    if (!document) return

    // Detect changes
    const changes = detectChanges()

    if (changes.length > 0) {
      // Show change confirmation modal
      setPendingChanges(changes)
      setShowChangeConfirmation(true)
      return
    }

    // No changes, proceed directly
    await confirmDocument()
  }, [
    document,
    detectChanges,
    setPendingChanges,
    setShowChangeConfirmation,
    confirmDocument,
  ])

  // Keyboard navigation (defined after handlers)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'ArrowLeft':
            event.preventDefault()
            handlePrevious()
            break
          case 'ArrowRight':
            event.preventDefault()
            handleNext()
            break
          case 'Enter':
            event.preventDefault()
            if (!submitting) {
              void handleConfirm()
            }
            break
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [
    currentIndex,
    reviewableDocuments,
    submitting,
    handleConfirm,
    handleNext,
    handlePrevious,
  ])

  // Handle change confirmation modal actions
  const handleConfirmChanges = () => {
    void confirmDocument()
  }

  const handleCancelChanges = () => {
    setShowChangeConfirmation(false)
    setPendingChanges([])
  }

  if (loading) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          backgroundColor: zenTheme.bg,
        }}
      >
        <div style={{ color: zenTheme.secondaryText }}>Loading document...</div>
      </div>
    )
  }

  if (error && !document) {
    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          backgroundColor: zenTheme.bg,
          gap: '16px',
        }}
      >
        <div style={{ color: zenTheme.error }}>Error: {error}</div>
        <button
          onClick={handleBack}
          style={{
            padding: '8px 16px',
            backgroundColor: zenTheme.primaryAction,
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
          }}
        >
          Back to Inbox
        </button>
      </div>
    )
  }

  return (
    <div
      style={{
        height: '100vh',
        backgroundColor: zenTheme.bg,
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {/* Header */}
      <div
        style={{
          backgroundColor: zenTheme.surface,
          borderBottom: `1px solid ${zenTheme.border}`,
          padding: '16px 24px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          boxShadow: zenTheme.shadow,
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <button
            onClick={handleBack}
            style={{
              padding: '8px',
              backgroundColor: 'transparent',
              border: 'none',
              color: zenTheme.secondaryText,
              cursor: 'pointer',
              borderRadius: '6px',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            ← Back to Inbox
          </button>
          <div>
            <h1
              style={{
                margin: 0,
                fontSize: '20px',
                fontWeight: 600,
                color: zenTheme.primaryText,
              }}
            >
              Review Document
            </h1>
            <p
              style={{
                margin: 0,
                fontSize: '14px',
                color: zenTheme.secondaryText,
              }}
            >
              {formData.supplierName ||
                document?.supplier_name ||
                'Unknown Supplier'}{' '}
              -{' '}
              {formData.invoiceNumber ||
                document?.invoice_number ||
                'No Invoice Number'}
            </p>
          </div>
        </div>

        <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
          {/* Document counter */}
          {reviewableDocuments.length > 0 && currentIndex >= 0 && (
            <div
              style={{
                fontSize: '14px',
                color: zenTheme.secondaryText,
                padding: '8px 12px',
              }}
            >
              {currentIndex + 1} of {reviewableDocuments.length}
            </div>
          )}

          {/* Navigation buttons */}
          <button
            onClick={handlePrevious}
            disabled={currentIndex <= 0}
            title="Previous document (Ctrl+←)"
            style={{
              padding: '8px 12px',
              backgroundColor: 'transparent',
              border: `1px solid ${zenTheme.border}`,
              color:
                currentIndex <= 0
                  ? zenTheme.subtleText
                  : zenTheme.secondaryText,
              cursor: currentIndex <= 0 ? 'not-allowed' : 'pointer',
              borderRadius: '6px',
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              opacity: currentIndex <= 0 ? 0.5 : 1,
            }}
          >
            ← Previous
          </button>

          <button
            onClick={handleNext}
            disabled={currentIndex >= reviewableDocuments.length - 1}
            title="Next document (Ctrl+→)"
            style={{
              padding: '8px 12px',
              backgroundColor: 'transparent',
              border: `1px solid ${zenTheme.border}`,
              color:
                currentIndex >= reviewableDocuments.length - 1
                  ? zenTheme.subtleText
                  : zenTheme.secondaryText,
              cursor:
                currentIndex >= reviewableDocuments.length - 1
                  ? 'not-allowed'
                  : 'pointer',
              borderRadius: '6px',
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              opacity: currentIndex >= reviewableDocuments.length - 1 ? 0.5 : 1,
            }}
          >
            Next →
          </button>

          {/* Confirm button */}
          <button
            onClick={() => {
              void handleConfirm()
            }}
            disabled={submitting}
            title="Confirm document (Ctrl+Enter)"
            style={{
              padding: '8px 16px',
              backgroundColor: submitting
                ? zenTheme.subtleText
                : zenTheme.primaryAction,
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: submitting ? 'not-allowed' : 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
            }}
          >
            {submitting ? '⏳' : ZenIcons.review({ size: 14 })}
            {submitting ? 'Confirming...' : 'Confirm'}
          </button>
        </div>
      </div>

      {/* Success/Error Messages */}
      {(success || error) && (
        <div
          style={{
            padding: '12px 24px',
            backgroundColor: success ? '#f0fdf4' : '#fef2f2',
            borderBottom: `1px solid ${success ? zenTheme.success : zenTheme.error}`,
            color: success ? zenTheme.success : zenTheme.error,
            fontSize: '14px',
          }}
        >
          {success || error}
        </div>
      )}

      {/* Main Content - Split Screen */}
      <div
        style={{
          flex: 1,
          display: 'flex',
          overflow: 'hidden',
        }}
      >
        {/* Left Panel - Form Fields */}
        <div
          style={{
            width: '50%',
            backgroundColor: zenTheme.surface,
            borderRight: `1px solid ${zenTheme.border}`,
            overflow: 'auto',
            padding: '24px',
          }}
        >
          <div style={{ maxWidth: '500px' }}>
            {activeExtraction?.confidenceBreakdown && (
              <div
                style={{
                  marginBottom: '24px',
                  padding: '12px 16px',
                  borderRadius: '8px',
                  border: `1px solid ${zenTheme.border}`,
                  backgroundColor: zenTheme.bg,
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    marginBottom: '8px',
                  }}
                >
                  <span
                    style={{
                      fontSize: '13px',
                      fontWeight: 600,
                      color: zenTheme.secondaryText,
                    }}
                  >
                    Document confidence
                  </span>
                  <span
                    style={{
                      fontSize: '13px',
                      fontWeight: 600,
                      color: zenTheme.primaryText,
                    }}
                  >
                    {(
                      activeExtraction.confidenceBreakdown.document * 100
                    ).toFixed(0)}
                    %
                  </span>
                </div>
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                  {Object.entries(
                    activeExtraction.confidenceBreakdown.fields || {}
                  ).map(([key, value]) => (
                    <span
                      key={key}
                      style={{
                        fontSize: '12px',
                        padding: '4px 8px',
                        borderRadius: '12px',
                        backgroundColor: '#f3f4f6',
                        color: zenTheme.secondaryText,
                      }}
                    >
                      {key}: {(value * 100).toFixed(0)}%
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Supplier Information */}
            <div style={{ marginBottom: '32px' }}>
              <h3
                style={{
                  fontSize: '18px',
                  fontWeight: 600,
                  color: zenTheme.primaryText,
                  marginBottom: '16px',
                  margin: '0 0 16px 0',
                }}
              >
                Supplier Information
              </h3>

              {renderCandidateChips('supplier', 'Supplier')}

              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: '1fr 1fr',
                  gap: '16px',
                  marginBottom: '16px',
                }}
              >
                <div>
                  <label
                    style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: 500,
                      color: zenTheme.primaryText,
                      marginBottom: '6px',
                    }}
                  >
                    Supplier Name *
                  </label>
                  <input
                    type="text"
                    value={formData.supplierName}
                    onChange={e =>
                      handleFieldChange('supplierName', e.target.value)
                    }
                    placeholder="Enter supplier name"
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: `1px solid ${formErrors.supplierName ? zenTheme.error : zenTheme.border}`,
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: zenTheme.surface,
                      color: zenTheme.primaryText,
                      outline: 'none',
                      transition: 'border-color 0.15s ease',
                    }}
                    onFocus={e => {
                      handleFieldFocus('supplier')
                      e.target.style.borderColor = zenTheme.primaryAction
                    }}
                    onBlur={e =>
                      (e.target.style.borderColor = formErrors.supplierName
                        ? zenTheme.error
                        : zenTheme.border)
                    }
                  />
                  {formErrors.supplierName && (
                    <div
                      style={{
                        color: zenTheme.error,
                        fontSize: '12px',
                        marginTop: '4px',
                      }}
                    >
                      {formErrors.supplierName}
                    </div>
                  )}
                </div>

                <div>
                  <label
                    style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: 500,
                      color: zenTheme.primaryText,
                      marginBottom: '6px',
                    }}
                  >
                    VAT Number
                  </label>
                  <input
                    type="text"
                    value={formData.supplierVat}
                    onChange={e =>
                      handleFieldChange('supplierVat', e.target.value)
                    }
                    placeholder="BE0123456789"
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: `1px solid ${zenTheme.border}`,
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: zenTheme.surface,
                      color: zenTheme.primaryText,
                      outline: 'none',
                      transition: 'border-color 0.15s ease',
                    }}
                    onFocus={e => {
                      handleFieldFocus('supplier')
                      e.target.style.borderColor = zenTheme.primaryAction
                    }}
                    onBlur={e => (e.target.style.borderColor = zenTheme.border)}
                  />
                </div>
              </div>

              <div>
                <label
                  style={{
                    display: 'block',
                    fontSize: '14px',
                    fontWeight: 500,
                    color: zenTheme.primaryText,
                    marginBottom: '6px',
                  }}
                >
                  Address
                </label>
                <textarea
                  value={formData.supplierAddress}
                  onChange={e =>
                    handleFieldChange('supplierAddress', e.target.value)
                  }
                  placeholder="Enter supplier address"
                  rows={3}
                  style={{
                    width: '100%',
                    padding: '8px 12px',
                    border: `1px solid ${zenTheme.border}`,
                    borderRadius: '6px',
                    fontSize: '14px',
                    backgroundColor: zenTheme.surface,
                    color: zenTheme.primaryText,
                    outline: 'none',
                    transition: 'border-color 0.15s ease',
                    resize: 'vertical',
                    fontFamily: 'inherit',
                  }}
                  onFocus={e => {
                    handleFieldFocus('supplier')
                    e.target.style.borderColor = zenTheme.primaryAction
                  }}
                  onBlur={e => (e.target.style.borderColor = zenTheme.border)}
                />
              </div>
            </div>

            {/* Payment Instructions */}
            <div style={{ marginBottom: '32px' }}>
              <h3
                style={{
                  fontSize: '18px',
                  fontWeight: 600,
                  color: zenTheme.primaryText,
                  marginBottom: '16px',
                  margin: '0 0 16px 0',
                }}
              >
                Payment Instructions
              </h3>

              {renderCandidateChips('payment', 'Payment')}

              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: '1fr 1fr',
                  gap: '16px',
                  marginBottom: '16px',
                }}
              >
                <div>
                  <label
                    style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: 500,
                      color: zenTheme.primaryText,
                      marginBottom: '6px',
                    }}
                  >
                    IBAN
                  </label>
                  <input
                    type="text"
                    value={formData.paymentIban}
                    onChange={e =>
                      handleFieldChange('paymentIban', e.target.value)
                    }
                    placeholder="BE00 0000 0000 0000"
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: `1px solid ${zenTheme.border}`,
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: zenTheme.surface,
                      color: zenTheme.primaryText,
                      outline: 'none',
                      transition: 'border-color 0.15s ease',
                    }}
                    onFocus={e => {
                      handleFieldFocus('payment')
                      e.target.style.borderColor = zenTheme.primaryAction
                    }}
                    onBlur={e => (e.target.style.borderColor = zenTheme.border)}
                  />
                </div>

                <div>
                  <label
                    style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: 500,
                      color: zenTheme.primaryText,
                      marginBottom: '6px',
                    }}
                  >
                    BIC
                  </label>
                  <input
                    type="text"
                    value={formData.paymentBic}
                    onChange={e =>
                      handleFieldChange('paymentBic', e.target.value)
                    }
                    placeholder="BBRUBEBB"
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: `1px solid ${zenTheme.border}`,
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: zenTheme.surface,
                      color: zenTheme.primaryText,
                      outline: 'none',
                      transition: 'border-color 0.15s ease',
                    }}
                    onFocus={e => {
                      handleFieldFocus('payment')
                      e.target.style.borderColor = zenTheme.primaryAction
                    }}
                    onBlur={e => (e.target.style.borderColor = zenTheme.border)}
                  />
                </div>
              </div>

              <div>
                <label
                  style={{
                    display: 'block',
                    fontSize: '14px',
                    fontWeight: 500,
                    color: zenTheme.primaryText,
                    marginBottom: '6px',
                  }}
                >
                  Structured Reference
                </label>
                <input
                  type="text"
                  value={formData.paymentStructuredRef}
                  onChange={e =>
                    handleFieldChange('paymentStructuredRef', e.target.value)
                  }
                  placeholder="+++123/4567/89012+++"
                  style={{
                    width: '100%',
                    padding: '8px 12px',
                    border: `1px solid ${zenTheme.border}`,
                    borderRadius: '6px',
                    fontSize: '14px',
                    backgroundColor: zenTheme.surface,
                    color: zenTheme.primaryText,
                    outline: 'none',
                    transition: 'border-color 0.15s ease',
                  }}
                  onFocus={e => {
                    handleFieldFocus('payment')
                    e.target.style.borderColor = zenTheme.primaryAction
                  }}
                  onBlur={e => (e.target.style.borderColor = zenTheme.border)}
                />
              </div>
            </div>

            {/* Invoice Information */}
            <div style={{ marginBottom: '32px' }}>
              <h3
                style={{
                  fontSize: '18px',
                  fontWeight: 600,
                  color: zenTheme.primaryText,
                  marginBottom: '16px',
                  margin: '0 0 16px 0',
                }}
              >
                Invoice Information
              </h3>

              {renderCandidateChips('invoice', 'Invoice meta')}

              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: '1fr 1fr',
                  gap: '16px',
                  marginBottom: '16px',
                }}
              >
                <div>
                  <label
                    style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: 500,
                      color: zenTheme.primaryText,
                      marginBottom: '6px',
                    }}
                  >
                    Invoice Number *
                  </label>
                  <input
                    type="text"
                    value={formData.invoiceNumber}
                    onChange={e =>
                      handleFieldChange('invoiceNumber', e.target.value)
                    }
                    placeholder="Enter invoice number"
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: `1px solid ${formErrors.invoiceNumber ? zenTheme.error : zenTheme.border}`,
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: zenTheme.surface,
                      color: zenTheme.primaryText,
                      outline: 'none',
                      transition: 'border-color 0.15s ease',
                    }}
                    onFocus={e => {
                      handleFieldFocus('invoice')
                      e.target.style.borderColor = zenTheme.primaryAction
                    }}
                    onBlur={e =>
                      (e.target.style.borderColor = formErrors.invoiceNumber
                        ? zenTheme.error
                        : zenTheme.border)
                    }
                  />
                  {formErrors.invoiceNumber && (
                    <div
                      style={{
                        color: zenTheme.error,
                        fontSize: '12px',
                        marginTop: '4px',
                      }}
                    >
                      {formErrors.invoiceNumber}
                    </div>
                  )}
                </div>

                <div>
                  <label
                    style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: 500,
                      color: zenTheme.primaryText,
                      marginBottom: '6px',
                    }}
                  >
                    Invoice Date *
                  </label>
                  <input
                    type="date"
                    value={formData.invoiceIssueDate}
                    onChange={e =>
                      handleFieldChange('invoiceIssueDate', e.target.value)
                    }
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: `1px solid ${formErrors.invoiceIssueDate ? zenTheme.error : zenTheme.border}`,
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: zenTheme.surface,
                      color: zenTheme.primaryText,
                      outline: 'none',
                      transition: 'border-color 0.15s ease',
                    }}
                    onFocus={e => {
                      handleFieldFocus('invoice')
                      e.target.style.borderColor = zenTheme.primaryAction
                    }}
                    onBlur={e =>
                      (e.target.style.borderColor = formErrors.invoiceIssueDate
                        ? zenTheme.error
                        : zenTheme.border)
                    }
                  />
                  {formErrors.invoiceIssueDate && (
                    <div
                      style={{
                        color: zenTheme.error,
                        fontSize: '12px',
                        marginTop: '4px',
                      }}
                    >
                      {formErrors.invoiceIssueDate}
                    </div>
                  )}
                </div>
              </div>

              {renderCandidateChips('totals', 'Totals')}

              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: '1fr 1fr 1fr',
                  gap: '16px',
                }}
              >
                <div>
                  <label
                    style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: 500,
                      color: zenTheme.primaryText,
                      marginBottom: '6px',
                    }}
                  >
                    Net Amount
                  </label>
                  <input
                    type="text"
                    value={formData.invoiceNet}
                    onChange={e =>
                      handleFieldChange('invoiceNet', e.target.value)
                    }
                    placeholder="0.00"
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: `1px solid ${zenTheme.border}`,
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: zenTheme.surface,
                      color: zenTheme.primaryText,
                      outline: 'none',
                      transition: 'border-color 0.15s ease',
                    }}
                    onFocus={e => {
                      handleFieldFocus('totals')
                      e.target.style.borderColor = zenTheme.primaryAction
                    }}
                    onBlur={e => (e.target.style.borderColor = zenTheme.border)}
                  />
                </div>

                <div>
                  <label
                    style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: 500,
                      color: zenTheme.primaryText,
                      marginBottom: '6px',
                    }}
                  >
                    VAT Amount
                  </label>
                  <input
                    type="text"
                    value={formData.invoiceVat}
                    onChange={e =>
                      handleFieldChange('invoiceVat', e.target.value)
                    }
                    placeholder="0.00"
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: `1px solid ${zenTheme.border}`,
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: zenTheme.surface,
                      color: zenTheme.primaryText,
                      outline: 'none',
                      transition: 'border-color 0.15s ease',
                    }}
                    onFocus={e => {
                      handleFieldFocus('totals')
                      e.target.style.borderColor = zenTheme.primaryAction
                    }}
                    onBlur={e => (e.target.style.borderColor = zenTheme.border)}
                  />
                </div>

                <div>
                  <label
                    style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: 500,
                      color: zenTheme.primaryText,
                      marginBottom: '6px',
                    }}
                  >
                    Gross Amount
                  </label>
                  <input
                    type="text"
                    value={formData.invoiceGross}
                    onChange={e =>
                      handleFieldChange('invoiceGross', e.target.value)
                    }
                    placeholder="0.00"
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: `1px solid ${zenTheme.border}`,
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: zenTheme.surface,
                      color: zenTheme.primaryText,
                      outline: 'none',
                      transition: 'border-color 0.15s ease',
                    }}
                    onFocus={e => {
                      handleFieldFocus('totals')
                      e.target.style.borderColor = zenTheme.primaryAction
                    }}
                    onBlur={e => (e.target.style.borderColor = zenTheme.border)}
                  />
                </div>
              </div>
            </div>

            {/* Suggested Journal Lines */}
            {formData.suggestionLines.length > 0 && (
              <div style={{ marginBottom: '32px' }}>
                <h3
                  style={{
                    fontSize: '18px',
                    fontWeight: 600,
                    color: zenTheme.primaryText,
                    marginBottom: '16px',
                    margin: '0 0 16px 0',
                  }}
                >
                  Suggested Journal Lines
                </h3>

                <div
                  style={{
                    border: `1px solid ${zenTheme.border}`,
                    borderRadius: '6px',
                    overflow: 'hidden',
                  }}
                >
                  <div
                    style={{
                      backgroundColor: zenTheme.bg,
                      padding: '8px 12px',
                      fontSize: '12px',
                      fontWeight: 500,
                      color: zenTheme.secondaryText,
                      display: 'grid',
                      gridTemplateColumns: '1fr 80px 80px',
                      gap: '8px',
                    }}
                  >
                    <div>Account</div>
                    <div>Debit</div>
                    <div>Credit</div>
                  </div>

                  {formData.suggestionLines.map((line, index) => (
                    <div
                      key={index}
                      style={{
                        padding: '8px 12px',
                        fontSize: '14px',
                        color: zenTheme.primaryText,
                        display: 'grid',
                        gridTemplateColumns: '1fr 80px 80px',
                        gap: '8px',
                        borderTop:
                          index > 0 ? `1px solid ${zenTheme.border}` : 'none',
                      }}
                    >
                      <div>
                        <div style={{ fontWeight: 500 }}>
                          {getAccountDisplayName(
                            accounts,
                            line.accountId as unknown as number
                          )}
                        </div>
                        {line.memo && (
                          <div
                            style={{
                              fontSize: '12px',
                              color: zenTheme.secondaryText,
                              marginTop: '2px',
                            }}
                          >
                            {line.memo}
                          </div>
                        )}
                      </div>
                      <div>€{line.debit}</div>
                      <div>€{line.credit}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right Panel - Document Viewer */}
        <div
          style={{
            width: '50%',
            backgroundColor: zenTheme.bg,
            padding: '24px',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          {(activeExtraction?.pagePreviews?.length ?? 0) > 0 &&
          activeExtraction?.regions?.length ? (
            <div style={{ marginBottom: '16px' }}>
              <h3
                style={{
                  margin: '0 0 8px 0',
                  fontSize: '16px',
                  fontWeight: 600,
                  color: zenTheme.primaryText,
                }}
              >
                Region Map
              </h3>
              <div
                style={{
                  fontSize: '12px',
                  color: zenTheme.secondaryText,
                  marginBottom: '8px',
                }}
              >
                Click a region to focus. Candidates appear in the left pane.
              </div>
              {(() => {
                if (
                  !activeExtraction?.pagePreviews?.length ||
                  !activeExtraction?.regions?.length
                ) {
                  return null
                }
                const preview = activeExtraction.pagePreviews[0] as {
                  page: number
                  imageBase64?: string
                  image_base64?: string
                }
                const regionsForPage = activeExtraction.regions.filter(
                  region => region.page === preview.page
                )
                return (
                  <div
                    style={{
                      position: 'relative',
                      borderRadius: '12px',
                      overflow: 'hidden',
                      border: `1px solid ${zenTheme.border}`,
                    }}
                  >
                    <img
                      src={`data:image/jpeg;base64,${preview.imageBase64 || preview.image_base64 || ''}`}
                      alt="Invoice preview"
                      style={{ width: '100%', display: 'block' }}
                    />
                    {regionsForPage.map(region => {
                      const backgroundColor =
                        REGION_COLORS[region.label] || REGION_COLORS.OTHER
                      const isActive = region.id === highlightRegionId
                      const borderColor = isActive ? '#1f2937' : '#6b7280'
                      const bbox = region.bbox
                      return (
                        <div
                          key={region.id}
                          onClick={() => handleRegionClick(region.id)}
                          style={{
                            position: 'absolute',
                            left: `${bbox.x0 * 100}%`,
                            top: `${bbox.y0 * 100}%`,
                            width: `${(bbox.x1 - bbox.x0) * 100}%`,
                            height: `${(bbox.y1 - bbox.y0) * 100}%`,
                            border: `2px solid ${borderColor}`,
                            backgroundColor,
                            cursor: 'pointer',
                            boxShadow: isActive
                              ? '0 0 0 2px rgba(37, 99, 235, 0.35)'
                              : 'none',
                          }}
                          title={`${region.label} (${(region.score * 100).toFixed(0)}%)`}
                        >
                          <span
                            style={{
                              position: 'absolute',
                              left: '4px',
                              top: '2px',
                              fontSize: '10px',
                              fontWeight: 600,
                              color: '#1f2937',
                              backgroundColor: 'rgba(255,255,255,0.8)',
                              padding: '2px 4px',
                              borderRadius: '4px',
                            }}
                          >
                            {region.label}
                          </span>
                        </div>
                      )
                    })}
                  </div>
                )
              })()}
            </div>
          ) : null}

          {documentUrl ? (
            <div
              style={{
                width: '100%',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                gap: '12px',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  paddingBottom: '8px',
                  borderBottom: `1px solid ${zenTheme.border}`,
                }}
              >
                <h3
                  style={{
                    margin: 0,
                    fontSize: '16px',
                    fontWeight: 600,
                    color: zenTheme.primaryText,
                  }}
                >
                  Document Preview
                </h3>
                <button
                  onClick={() => window.open(documentUrl, '_blank')}
                  style={{
                    padding: '6px 12px',
                    backgroundColor: 'transparent',
                    border: `1px solid ${zenTheme.border}`,
                    borderRadius: '4px',
                    fontSize: '12px',
                    color: zenTheme.secondaryText,
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px',
                  }}
                >
                  {ZenIcons.view({ size: 12 })}
                  Open in New Tab
                </button>
              </div>

              {!iframeError ? (
                <iframe
                  src={documentUrl}
                  style={{
                    width: '100%',
                    height: '100%',
                    border: `1px solid ${zenTheme.border}`,
                    borderRadius: '8px',
                    backgroundColor: 'white',
                  }}
                  title="Document Preview"
                  onLoad={() => setIframeError(false)}
                  onError={() => setIframeError(true)}
                />
              ) : (
                <div
                  style={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    border: `1px solid ${zenTheme.border}`,
                    borderRadius: '8px',
                    backgroundColor: zenTheme.surface,
                    gap: '16px',
                  }}
                >
                  <div style={{ fontSize: '48px' }}>📄</div>
                  <div
                    style={{
                      fontSize: '16px',
                      fontWeight: 500,
                      color: zenTheme.primaryText,
                      textAlign: 'center',
                    }}
                  >
                    Document preview unavailable
                  </div>
                  <div
                    style={{
                      fontSize: '14px',
                      color: zenTheme.secondaryText,
                      textAlign: 'center',
                      maxWidth: '300px',
                    }}
                  >
                    The document cannot be displayed in the preview. Use the
                    &quot;Open in New Tab&quot; button to view it.
                  </div>
                  <button
                    onClick={() => setIframeError(false)}
                    style={{
                      padding: '8px 16px',
                      backgroundColor: zenTheme.primaryAction,
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      fontSize: '14px',
                      cursor: 'pointer',
                    }}
                  >
                    Retry Preview
                  </button>
                </div>
              )}
            </div>
          ) : (
            <div
              style={{
                width: '100%',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                color: zenTheme.secondaryText,
                textAlign: 'center',
                border: `2px dashed ${zenTheme.border}`,
                borderRadius: '12px',
                backgroundColor: zenTheme.surface,
              }}
            >
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>📄</div>
              <div
                style={{
                  fontSize: '16px',
                  fontWeight: 500,
                  marginBottom: '8px',
                }}
              >
                Document Preview
              </div>
              <div style={{ fontSize: '14px' }}>
                Document preview not available
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Change Confirmation Modal */}
      <ChangeConfirmationModal
        isOpen={showChangeConfirmation}
        changes={pendingChanges}
        onConfirm={handleConfirmChanges}
        onCancel={handleCancelChanges}
        loading={confirmingChanges}
      />
    </div>
  )
}
