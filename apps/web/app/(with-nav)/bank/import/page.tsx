'use client'

import {
  Dispatch,
  FormEvent,
  SetStateAction,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { createSecureBrowserClient } from '@/lib/session-security'
import { useOrgEntitySelection } from '@/hooks/useOrgEntitySelection'

type ImportFormat = 'coda' | 'csv'

interface BankAccountOption {
  id: number
  name: string | null
  iban: string | null
  account_number: string | null
}

type CsvFieldKey = keyof CsvMappingState

function renderCsvSelect(
  label: string,
  field: CsvFieldKey,
  required: boolean,
  mapping: CsvMappingState | null,
  headers: string[],
  setMapping: Dispatch<SetStateAction<CsvMappingState | null>>
) {
  // eslint-disable-next-line security/detect-object-injection
  const selectedValue = mapping?.[field] ?? ''

  return (
    <div>
      <label className="block text-xs font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500">*</span>}
      </label>
      <select
        value={selectedValue}
        onChange={event => {
          const value = event.target.value
          setMapping(prev => {
            const next = { ...(prev ?? {}) }
            if (value) {
              // eslint-disable-next-line security/detect-object-injection
              next[field] = value
            } else {
              // eslint-disable-next-line security/detect-object-injection
              delete next[field]
            }
            return next
          })
        }}
        className="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
      >
        <option value="">{required ? 'Select column' : 'Not mapped'}</option>
        {headers.map(header => (
          <option key={header} value={header}>
            {header}
          </option>
        ))}
      </select>
    </div>
  )
}

type CsvDetectionResult = {
  delimiter: string
  headers: string[]
  mapping: CsvMappingState
}

function detectCsvConfig(text: string): CsvDetectionResult {
  const lines = text.split(/\r?\n/).filter(line => line.trim().length > 0)
  if (lines.length < 2) {
    throw new Error('CSV must contain a header row and at least one data row')
  }

  const delimiter = detectDelimiter(lines[0] ?? '')
  const headers = splitCsvLine(lines[0] ?? '', delimiter)

  if (headers.length < 3) {
    throw new Error(
      'Could not read CSV headers. Please verify the delimiter or map columns manually.'
    )
  }

  const normalized = headers.map(header => ({
    header,
    key: normalizeHeader(header),
  }))

  const findHeader = (...candidates: string[]) =>
    normalized.find(item =>
      candidates.some(candidate => item.key.includes(candidate))
    )?.header

  const bookingDate =
    findHeader('transactiondate', 'bookingdate', 'boekingsdatum', 'date') ||
    undefined
  const valueDate = findHeader('valuedate', 'valuedatum', 'value') || undefined
  const amount =
    findHeader('amount', 'bedrag', 'montant', 'total', 'sum', 'debitcredit') ||
    undefined
  const reference =
    findHeader(
      'description',
      'omschrijving',
      'communication',
      'memo',
      'details',
      'note'
    ) ||
    findHeader('narration', 'narrative') ||
    undefined
  const counterparty =
    findHeader(
      'counterparty',
      'beneficiary',
      'payee',
      'tegenpartij',
      'customer',
      'supplier'
    ) || undefined

  if (!bookingDate || !amount || !reference) {
    throw new Error(
      'Could not automatically detect booking date, amount, and description columns. Map them manually below.'
    )
  }

  return {
    delimiter,
    headers,
    mapping: {
      bookingDate,
      valueDate,
      amount,
      reference,
      counterparty,
    },
  }
}

function extractCsvHeaders(text: string): {
  delimiter: string
  headers: string[]
} {
  const lines = text.split(/\r?\n/).filter(line => line.trim().length > 0)
  if (lines.length === 0) {
    return { delimiter: ',', headers: [] }
  }

  const delimiter = detectDelimiter(lines[0] ?? '')
  const headers = splitCsvLine(lines[0] ?? '', delimiter)
  return { delimiter, headers }
}

function detectDelimiter(line: string): string {
  const commaCount = (line.match(/,/g) || []).length
  const semicolonCount = (line.match(/;/g) || []).length
  const tabCount = (line.match(/\t/g) || []).length

  if (semicolonCount > commaCount && semicolonCount >= tabCount) return ';'
  if (tabCount > commaCount && tabCount > semicolonCount) return '\t'
  return ','
}

function splitCsvLine(line: string, delimiter: string): string[] {
  const values: string[] = []
  let current = ''
  let inQuotes = false

  for (let i = 0; i < line.length; i++) {
    // eslint-disable-next-line security/detect-object-injection
    const char = line[i] ?? ''

    if (char === '"') {
      if (inQuotes && line[i + 1] === '"') {
        current += '"'
        i++
      } else {
        inQuotes = !inQuotes
      }
      continue
    }

    if (char === delimiter && !inQuotes) {
      values.push(current.trim())
      current = ''
    } else {
      current += char
    }
  }

  values.push(current.trim())
  return values
}

function normalizeHeader(header: string): string {
  return header
    .trim()
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '')
}

interface ImportApiSuccess {
  imported: number
  skipped: number
  deduped: number
  warnings: string[]
  errors?: string[]
  batch_id: string
}

interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
}

type CsvMappingState = {
  bookingDate?: string
  valueDate?: string
  amount?: string
  reference?: string
  counterparty?: string
}

export default function BankImportPage() {
  const {
    currentEntity,
    selection,
    loading: selectionLoading,
    isValid,
  } = useOrgEntitySelection()

  const supabase = useMemo(() => createSecureBrowserClient(), [])

  const [format, setFormat] = useState<ImportFormat>('coda')
  const [accounts, setAccounts] = useState<BankAccountOption[]>([])
  const [accountsLoading, setAccountsLoading] = useState(false)
  const [accountsError, setAccountsError] = useState<string | null>(null)

  const [selectedAccountId, setSelectedAccountId] = useState<number | ''>('')
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [csvHeaders, setCsvHeaders] = useState<string[]>([])
  const [csvDelimiter, setCsvDelimiter] = useState<string>(',')
  const [csvMapping, setCsvMapping] = useState<CsvMappingState | null>(null)
  const [csvDetectionError, setCsvDetectionError] = useState<string | null>(
    null
  )

  const [submitError, setSubmitError] = useState<string | null>(null)
  const [submitWarnings, setSubmitWarnings] = useState<string[]>([])
  const [submitResult, setSubmitResult] = useState<ImportApiSuccess | null>(
    null
  )
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    let isMounted = true

    async function loadAccounts(entityId: number) {
      setAccountsLoading(true)
      setAccountsError(null)

      try {
        const { data, error } = await supabase
          .from('bank_accounts')
          .select('id, bank_name, iban, account_number')
          .eq('entity_id', entityId)
          .order('bank_name', { ascending: true })

        if (!isMounted) return

        if (error) {
          console.error('Failed to load bank accounts', error)
          setAccountsError('Could not load bank accounts for this entity')
          setAccounts([])
          return
        }

        const normalized = (data ?? []).map(account => ({
          id: account.id,
          name: account.bank_name ?? null,
          iban: account.iban ?? null,
          account_number: account.account_number ?? null,
        }))

        setAccounts(normalized)
        if (normalized.length === 1) {
          setSelectedAccountId(normalized[0]?.id ?? '')
        }
      } finally {
        if (isMounted) {
          setAccountsLoading(false)
        }
      }
    }

    if (selection.entityId) {
      void loadAccounts(selection.entityId)
    } else {
      setAccounts([])
      setSelectedAccountId('')
      setAccountsError(null)
      setAccountsLoading(false)
    }

    return () => {
      isMounted = false
    }
  }, [selection.entityId, supabase])

  useEffect(() => {
    // Reset CSV-specific state when switching formats
    if (format === 'coda') {
      setCsvHeaders([])
      setCsvMapping(null)
      setCsvDetectionError(null)
      setSelectedFile(null)
    }
  }, [format])

  const handleFormatChange = (value: ImportFormat) => {
    setFormat(value)
    setSelectedFile(null)
    if (value === 'csv') {
      setCsvHeaders([])
      setCsvMapping(null)
      setCsvDetectionError(null)
    }
    setSubmitError(null)
    setSubmitWarnings([])
    setSubmitResult(null)
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSubmitError(null)
    setSubmitWarnings([])
    setSubmitResult(null)

    const file = event.target.files?.[0] || null
    setSelectedFile(file)

    if (file && format === 'csv') {
      void file
        .text()
        .then(text => {
          try {
            const detected = detectCsvConfig(text)
            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
            setCsvHeaders(detected.headers)
            setCsvDelimiter(detected.delimiter)
            setCsvMapping(detected.mapping)
            setCsvDetectionError(null)
          } catch (error) {
            const fallback = extractCsvHeaders(text)
            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
            setCsvHeaders(fallback.headers)
            setCsvDelimiter(fallback.delimiter)
            setCsvMapping(null)
            setCsvDetectionError(
              error instanceof Error
                ? error.message
                : 'Unable to analyse CSV file. Please configure mappings manually.'
            )
          }
        })
        .catch(error => {
          // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
          console.error('Failed to read CSV file', error)
          setCsvHeaders([])
          setCsvMapping(null)
          setCsvDetectionError(
            'Could not read CSV file. Try selecting it again.'
          )
        })
    }
  }

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault()

    setSubmitError(null)
    setSubmitWarnings([])
    setSubmitResult(null)

    if (!selection.entityId) {
      setSubmitError(
        'Please select a company before importing a bank statement.'
      )
      return
    }

    if (!selectedAccountId) {
      setSubmitError(
        'Select the bank account that should receive these transactions.'
      )
      return
    }

    if (!selectedFile) {
      setSubmitError('Pick a bank statement file to import.')
      return
    }

    if (format === 'csv') {
      const mapping = csvMapping
      if (
        !mapping ||
        !mapping.bookingDate ||
        !mapping.amount ||
        !mapping.reference
      ) {
        setSubmitError(
          'Select the CSV columns for booking date, amount, and description.'
        )
        return
      }
    }

    try {
      setIsSubmitting(true)

      await supabase.auth.getSession()

      const formData = new FormData()
      formData.append('entity_id', String(selection.entityId))
      formData.append('bank_account_id', String(selectedAccountId))
      formData.append('format', format)
      formData.append('file', selectedFile)

      if (format === 'csv' && csvMapping) {
        const payload = {
          delimiter: csvDelimiter,
          headers: true,
          map: {
            bookingDate: csvMapping.bookingDate,
            valueDate: csvMapping.valueDate || undefined,
            amount: csvMapping.amount,
            reference: csvMapping.reference,
            counterparty: csvMapping.counterparty || undefined,
          },
        }
        formData.append('csv_config', JSON.stringify(payload))
      }

      const response = await fetch(
        `/api/entities/${selection.entityId}/bank-transactions/import`,
        {
          method: 'POST',
          body: formData,
        }
      )

      if (!response.ok) {
        const payload = (await response
          .json()
          .catch(() => null)) as ApiResponse<unknown> | null
        const errorMessage =
          payload?.error || `Import failed with status ${response.status}`
        throw new Error(errorMessage)
      }

      const payload = (await response.json()) as ApiResponse<ImportApiSuccess>
      const data = payload.data

      if (!payload.success || !data) {
        throw new Error(payload.error || 'Import did not return a result')
      }

      setSubmitResult(data)
      setSubmitWarnings(data.warnings || [])
      setSelectedFile(null)
      if (format === 'csv') {
        setCsvHeaders([])
        setCsvMapping(null)
      }
    } catch (error) {
      console.error('Bank statement import failed', error)
      setSubmitError(
        error instanceof Error
          ? error.message
          : 'Something went wrong while importing the statement.'
      )
    } finally {
      setIsSubmitting(false)
    }
  }

  if (selectionLoading) {
    return (
      <div className="max-w-3xl mx-auto py-16">
        <p className="text-sm text-gray-600">Loading organization details…</p>
      </div>
    )
  }

  if (!isValid || !selection.entityId || !currentEntity) {
    return (
      <div className="max-w-3xl mx-auto py-16">
        <h1 className="text-2xl font-semibold text-gray-900 mb-3">
          Select a company to import bank statements
        </h1>
        <p className="text-gray-600">
          Choose a tenant and company from the selector in the top navigation,
          then return to this page to upload CODA statements.
        </p>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto py-10">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Import bank statements
        </h1>
        <p className="text-gray-600 max-w-2xl">
          Upload Belgian CODA files to ingest transactions into Ledgerly. The
          importer validates balances, structured references, and deduplicates
          repeat uploads automatically.
        </p>
      </div>

      <div className="bg-white border border-gray-200 rounded-xl shadow-sm p-6">
        <form
          onSubmit={e => {
            void handleSubmit(e)
          }}
          className="space-y-6"
        >
          <div>
            <label className="block text-sm font-medium text-gray-700">
              File format
            </label>
            <div className="mt-2 flex items-center gap-6">
              {(['coda', 'csv'] as ImportFormat[]).map(option => (
                <label
                  key={option}
                  className="inline-flex items-center gap-2 text-sm text-gray-700"
                >
                  <input
                    type="radio"
                    name="format"
                    value={option}
                    checked={format === option}
                    onChange={() => handleFormatChange(option)}
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                  />
                  {option === 'coda' ? 'CODA (.coda/.txt)' : 'CSV (.csv)'}
                </label>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">
              Company
            </label>
            <div className="mt-1 text-sm text-gray-900">
              {currentEntity.entity_name}
            </div>
            <p className="mt-1 text-xs text-gray-500">
              You can switch companies from the selector in the header.
            </p>
          </div>

          <div>
            <label
              htmlFor="bank-account"
              className="block text-sm font-medium text-gray-700"
            >
              Bank account to import into
            </label>
            <select
              id="bank-account"
              name="bank-account"
              value={selectedAccountId}
              onChange={event => {
                const value = event.target.value
                setSelectedAccountId(value ? Number(value) : '')
              }}
              disabled={accountsLoading}
              className="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
            >
              <option value="">
                {accountsLoading
                  ? 'Loading accounts…'
                  : 'Select a bank account'}
              </option>
              {accounts.map(account => (
                <option key={account.id} value={account.id}>
                  {account.name ||
                    account.account_number ||
                    account.iban ||
                    `Account ${account.id}`}
                </option>
              ))}
            </select>
            {accountsError && (
              <p className="mt-2 text-sm text-red-600">{accountsError}</p>
            )}
            {!accountsLoading && accounts.length === 0 && !accountsError && (
              <p className="mt-2 text-sm text-gray-500">
                No bank accounts yet. Create one in Supabase or through the
                accounting settings before importing statements.
              </p>
            )}
          </div>

          <div>
            <label
              htmlFor="import-file"
              className="block text-sm font-medium text-gray-700"
            >
              Bank statement file
            </label>
            <input
              id="import-file"
              name="import-file"
              type="file"
              accept={format === 'coda' ? '.coda,.cod,.txt' : '.csv,.txt'}
              onChange={handleFileChange}
              className="mt-1 block w-full text-sm text-gray-900 border border-gray-300 rounded-md cursor-pointer focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
            />
            <p className="mt-2 text-xs text-gray-500">
              {format === 'coda'
                ? 'Supports CODA v2 files. Each upload is processed once; reuploads will be deduplicated automatically.'
                : 'Upload comma, semicolon, or tab-delimited CSV exports. Map the columns below before importing.'}
            </p>
            {selectedFile && (
              <p className="mt-2 text-sm text-gray-600">
                Selected file:{' '}
                <span className="font-medium">{selectedFile.name}</span>
              </p>
            )}
          </div>

          {format === 'csv' && (
            <div className="space-y-4 rounded-lg border border-gray-200 bg-gray-50 p-4">
              <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm font-semibold text-gray-800">
                    CSV column mapping
                  </p>
                  <p className="text-xs text-gray-600">
                    Ensure each Ledgerly field points to the correct column in
                    your CSV export.
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <label
                    className="text-xs text-gray-600"
                    htmlFor="csv-delimiter"
                  >
                    Delimiter
                  </label>
                  <select
                    id="csv-delimiter"
                    value={csvDelimiter}
                    onChange={event => setCsvDelimiter(event.target.value)}
                    className="rounded-md border border-gray-300 bg-white px-2 py-1 text-xs shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
                  >
                    <option value=",">Comma (,)</option>
                    <option value=";">Semicolon (;)</option>
                    <option value="\t">Tab</option>
                  </select>
                </div>
              </div>

              {csvDetectionError && (
                <p className="text-xs text-red-600">{csvDetectionError}</p>
              )}

              {csvHeaders.length > 0 ? (
                <div className="grid gap-4 sm:grid-cols-2">
                  {renderCsvSelect(
                    'Booking date',
                    'bookingDate',
                    true,
                    csvMapping,
                    csvHeaders,
                    setCsvMapping
                  )}
                  {renderCsvSelect(
                    'Value date (optional)',
                    'valueDate',
                    false,
                    csvMapping,
                    csvHeaders,
                    setCsvMapping
                  )}
                  {renderCsvSelect(
                    'Amount',
                    'amount',
                    true,
                    csvMapping,
                    csvHeaders,
                    setCsvMapping
                  )}
                  {renderCsvSelect(
                    'Description',
                    'reference',
                    true,
                    csvMapping,
                    csvHeaders,
                    setCsvMapping
                  )}
                  {renderCsvSelect(
                    'Counterparty (optional)',
                    'counterparty',
                    false,
                    csvMapping,
                    csvHeaders,
                    setCsvMapping
                  )}
                </div>
              ) : (
                <p className="text-xs text-gray-600">
                  Select a CSV file to load available column names.
                </p>
              )}
            </div>
          )}

          {submitError && (
            <div className="rounded-md bg-red-50 border border-red-200 p-4 text-sm text-red-700">
              {submitError}
            </div>
          )}

          <div>
            <button
              type="submit"
              disabled={isSubmitting || !selectedFile || !selectedAccountId}
              className="inline-flex items-center rounded-md bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:bg-indigo-300"
            >
              {isSubmitting ? 'Importing…' : 'Import statement'}
            </button>
            {isSubmitting && (
              <div className="mt-3 flex items-center gap-2 text-xs text-gray-600">
                <span className="inline-block h-4 w-4 animate-spin rounded-full border-2 border-indigo-500 border-t-transparent"></span>
                Upload in progress…
              </div>
            )}
          </div>
        </form>
      </div>

      {submitResult && (
        <div className="mt-8 bg-white border border-gray-200 rounded-xl shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-900">Import status</h2>
          <dl className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div className="rounded-lg border border-gray-100 bg-gray-50 p-4">
              <dt className="text-xs uppercase tracking-wide text-gray-500">
                Imported
              </dt>
              <dd className="mt-1 text-2xl font-semibold text-gray-900">
                {submitResult.imported}
              </dd>
            </div>
            <div className="rounded-lg border border-gray-100 bg-gray-50 p-4">
              <dt className="text-xs uppercase tracking-wide text-gray-500">
                Deduplicated
              </dt>
              <dd className="mt-1 text-2xl font-semibold text-gray-900">
                {submitResult.deduped}
              </dd>
            </div>
            <div className="rounded-lg border border-gray-100 bg-gray-50 p-4">
              <dt className="text-xs uppercase tracking-wide text-gray-500">
                Skipped
              </dt>
              <dd className="mt-1 text-2xl font-semibold text-gray-900">
                {submitResult.skipped}
              </dd>
            </div>
          </dl>
          <p className="mt-4 text-xs text-gray-500">
            Batch ID: <span className="font-mono">{submitResult.batch_id}</span>
          </p>

          {submitWarnings.length > 0 && (
            <div className="mt-6 rounded-md border border-yellow-200 bg-yellow-50 p-4">
              <h3 className="text-sm font-medium text-yellow-800">
                Parser warnings
              </h3>
              <ul className="mt-2 space-y-1 text-sm text-yellow-900 list-disc list-inside">
                {submitWarnings.map((warning, index) => (
                  <li key={`${warning}-${index}`}>{warning}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
