'use client'

import { useEffect, useMemo, useState } from 'react'
import { useOrgEntitySelection } from '@/hooks/useOrgEntitySelection'
import { createSecureBrowserClient } from '@/lib/session-security'

const MATCH_LIMIT = 50

const TRANSACTION_STATUS_FULL_MATCH = 'matched'
const TRANSACTION_STATUS_PARTIAL = 'proposed'
const BULK_RECONCILED_STATUS = 'reconciled'
const AMOUNT_TOLERANCE = 0.01

type TransactionRow = {
  id: number
  bank_account_id: number
  transaction_date: string
  value_date: string | null
  amount: number
  description: string | null
  reference: string | null
  counterparty_name: string | null
  counterparty_account: string | null
  structured_ref: string | null
}

type InvoiceRow = {
  id: number
  invoice_date: string
  due_date: string | null
  total_amount: number
  status: string
  kind: 'sale' | 'purchase'
  number: string
  counterparty_name: string
}

type MatchState = {
  transactions: TransactionRow[]
  invoices: InvoiceRow[]
}

type MatchSuggestion = {
  id: number
  confidence: number
  recommended_amount: number
  reason: Record<string, unknown> | null
  bank_transactions: TransactionRow
  invoices: InvoiceRow
}

export default function ReconciliationPage() {
  const {
    selection,
    currentEntity,
    loading: selectionLoading,
    isValid,
  } = useOrgEntitySelection()
  const supabase = useMemo(() => createSecureBrowserClient(), [])

  const [data, setData] = useState<MatchState>({
    transactions: [],
    invoices: [],
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [reloadKey, setReloadKey] = useState(0)

  const [suggestions, setSuggestions] = useState<MatchSuggestion[]>([])
  const [suggestionsLoading, setSuggestionsLoading] = useState(false)
  const [suggestionActionId, setSuggestionActionId] = useState<number | null>(
    null
  )

  const [transactionSearch, setTransactionSearch] = useState('')
  const [invoiceSearch, setInvoiceSearch] = useState('')
  const [selectedTransactions, setSelectedTransactions] = useState<number[]>([])
  const [selectedInvoices, setSelectedInvoices] = useState<number[]>([])
  const [allocationAmount, setAllocationAmount] = useState('')

  const [actionMessage, setActionMessage] = useState<string | null>(null)
  const [actionError, setActionError] = useState<string | null>(null)

  useEffect(() => {
    if (!selection.entityId) return

    let cancelled = false
    const entityId = selection.entityId

    const loadData = async () => {
      try {
        setLoading(true)
        setError(null)

        const [txs, invoices] = await Promise.all([
          fetchTransactions(entityId, supabase),
          fetchInvoices(entityId, supabase),
        ])

        if (!cancelled) {
          setData({ transactions: txs, invoices })
          setSelectedTransactions([])
          setSelectedInvoices([])
        }
      } catch (fetchError) {
        if (!cancelled) {
          console.error('Failed to load reconciliation data', fetchError)
          setError(
            fetchError instanceof Error
              ? fetchError.message
              : 'Unable to load data'
          )
        }
      } finally {
        if (!cancelled) setLoading(false)
      }
    }

    void loadData()

    return () => {
      cancelled = true
    }
  }, [selection.entityId, supabase, reloadKey])

  useEffect(() => {
    if (!selection.entityId) {
      setSuggestions([])
      return
    }

    let cancelled = false
    const entityId = selection.entityId

    const loadSuggestions = async () => {
      try {
        setSuggestionsLoading(true)

        const response = await fetch(
          `/api/entities/${entityId}/bank-transactions/suggestions`
        )

        if (!response.ok) {
          throw new Error(
            `Failed to load suggestions (status ${response.status})`
          )
        }

        const payload = (await response.json()) as {
          success: boolean
          data?: unknown
          error?: string
        }

        if (!payload.success) {
          throw new Error(payload.error || 'Failed to load suggestions')
        }

        const suggestionsData = Array.isArray(payload.data) ? payload.data : []
        const normalized: MatchSuggestion[] = suggestionsData
          .map(item => normalizeSuggestion(item))
          .filter((value): value is MatchSuggestion => value !== null)

        if (!cancelled) {
          setSuggestions(normalized)
        }
      } catch (suggestionError) {
        if (!cancelled) {
          console.error('Failed to load suggestions', suggestionError)
          setSuggestions([])
        }
      } finally {
        if (!cancelled) {
          setSuggestionsLoading(false)
        }
      }
    }

    void loadSuggestions()

    return () => {
      cancelled = true
    }
  }, [selection.entityId, supabase, reloadKey])

  const filteredTransactions = useMemo(() => {
    const term = transactionSearch.trim().toLowerCase()
    if (!term) return data.transactions
    return data.transactions.filter(tx => {
      return [
        tx.description,
        tx.reference,
        tx.counterparty_name,
        tx.counterparty_account,
        tx.structured_ref,
      ]
        .filter(Boolean)
        .some(value => value!.toLowerCase().includes(term))
    })
  }, [data.transactions, transactionSearch])

  const filteredInvoices = useMemo(() => {
    const term = invoiceSearch.trim().toLowerCase()
    if (!term) return data.invoices
    return data.invoices.filter(inv => {
      return [inv.number, inv.counterparty_name]
        .filter(Boolean)
        .some(value => value.toLowerCase().includes(term))
    })
  }, [data.invoices, invoiceSearch])

  const selectedTransaction = useMemo(
    () => data.transactions.find(tx => tx.id === selectedTransactions[0]),
    [data.transactions, selectedTransactions]
  )
  const selectedInvoice = useMemo(
    () => data.invoices.find(inv => inv.id === selectedInvoices[0]),
    [data.invoices, selectedInvoices]
  )

  const allocationValue = Number.parseFloat(allocationAmount || '0')
  const maxTransactionAmount = selectedTransaction
    ? Math.abs(selectedTransaction.amount)
    : 0
  const maxInvoiceAmount = selectedInvoice
    ? Math.abs(Number(selectedInvoice.total_amount))
    : 0
  const allocationValid =
    selectedTransactions.length === 1 &&
    selectedInvoices.length === 1 &&
    Number.isFinite(allocationValue) &&
    allocationValue > 0 &&
    allocationValue <= maxTransactionAmount + AMOUNT_TOLERANCE &&
    (maxInvoiceAmount === 0 ||
      allocationValue <= maxInvoiceAmount + AMOUNT_TOLERANCE)
  const allocationTooLargeForInvoice =
    Boolean(selectedInvoice) &&
    allocationValue > maxInvoiceAmount + AMOUNT_TOLERANCE
  const canMatch =
    selectedTransactions.length === 1 &&
    selectedInvoices.length === 1 &&
    allocationValid

  useEffect(() => {
    if (!selectedTransaction) {
      setAllocationAmount('')
      return
    }

    const transactionAmount = Math.abs(selectedTransaction.amount)
    const invoiceAmount = selectedInvoice
      ? Math.abs(Number(selectedInvoice.total_amount))
      : transactionAmount
    const defaultAmount = Math.min(transactionAmount, invoiceAmount)

    setAllocationAmount(defaultAmount > 0 ? defaultAmount.toFixed(2) : '')
  }, [selectedTransaction, selectedInvoice])

  const amountsAligned = useMemo(() => {
    if (!selectedTransaction) return false
    if (!Number.isFinite(allocationValue) || allocationValue <= 0) return false
    return Math.abs(allocationValue - maxTransactionAmount) <= AMOUNT_TOLERANCE
  }, [allocationValue, maxTransactionAmount, selectedTransaction])

  const matchButtonLabel =
    selectedTransaction && allocationValid
      ? `Allocate ${formatCurrency(Math.max(allocationValue, 0))}`
      : 'Match selection'

  const handleTransactionSelect = (transactionId: number) => {
    setSelectedTransactions(prev =>
      prev.includes(transactionId)
        ? prev.filter(id => id !== transactionId)
        : [...prev, transactionId]
    )
  }

  const handleInvoiceSelect = (invoiceId: number) => {
    setSelectedInvoices(prev =>
      prev.includes(invoiceId)
        ? prev.filter(id => id !== invoiceId)
        : [...prev, invoiceId]
    )
  }

  const handleAcceptSuggestion = async (suggestion: MatchSuggestion) => {
    if (!selection.entityId) return
    setSuggestionActionId(suggestion.id)
    setActionError(null)
    setActionMessage(null)

    try {
      const response = await fetch(
        `/api/entities/${selection.entityId}/bank-transactions/suggestions/${suggestion.id}/accept`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            allocation_amount: suggestion.recommended_amount,
          }),
        }
      )

      if (!response.ok) {
        const payload = (await response.json().catch(() => null)) as {
          error?: string
        } | null
        throw new Error(
          payload?.error ||
            `Failed to accept suggestion (status ${response.status})`
        )
      }

      setSuggestions(prev =>
        prev.filter(current => current.id !== suggestion.id)
      )
      setActionMessage('Suggestion accepted and transaction reconciled.')
      setReloadKey(prev => prev + 1)
    } catch (acceptError) {
      console.error('Failed to accept suggestion', acceptError)
      setActionError(
        acceptError instanceof Error
          ? acceptError.message
          : 'Unable to accept suggestion.'
      )
    } finally {
      setSuggestionActionId(null)
    }
  }

  const handleRejectSuggestion = async (suggestion: MatchSuggestion) => {
    if (!selection.entityId) return
    setSuggestionActionId(suggestion.id)
    setActionError(null)

    try {
      const response = await fetch(
        `/api/entities/${selection.entityId}/bank-transactions/suggestions/${suggestion.id}/reject`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ reason: 'User rejected' }),
        }
      )

      if (!response.ok) {
        const payload = (await response.json().catch(() => null)) as {
          error?: string
        } | null
        throw new Error(
          payload?.error ||
            `Failed to reject suggestion (status ${response.status})`
        )
      }

      setSuggestions(prev =>
        prev.filter(current => current.id !== suggestion.id)
      )
    } catch (rejectError) {
      console.error('Failed to reject suggestion', rejectError)
      setActionError(
        rejectError instanceof Error
          ? rejectError.message
          : 'Unable to reject suggestion.'
      )
    } finally {
      setSuggestionActionId(null)
    }
  }

  const handleMatch = async () => {
    if (!selection.entityId || !selectedTransaction || !selectedInvoice) return
    setActionError(null)
    setActionMessage(null)

    try {
      if (!Number.isFinite(allocationValue) || allocationValue <= 0) {
        setActionError('Enter a positive amount to allocate.')
        return
      }

      if (allocationValue > maxTransactionAmount + AMOUNT_TOLERANCE) {
        setActionError('Allocation cannot exceed the transaction total.')
        return
      }

      if (allocationValue > maxInvoiceAmount + AMOUNT_TOLERANCE) {
        setActionError('Allocation cannot exceed the invoice balance.')
        return
      }

      await supabase.auth.getSession()

      const matchBody = {
        entity_id: selection.entityId,
        bank_transaction_id: selectedTransaction.id,
        kind: selectedInvoice.kind === 'sale' ? 'AR' : 'AP',
        invoice_id: selectedInvoice.id,
        amount_applied: allocationValue,
      }

      const response = await fetch(
        `/api/entities/${selection.entityId}/bank-transactions/${selectedTransaction.id}/links`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(matchBody),
        }
      )

      if (!response.ok) {
        const payload = (await response.json().catch(() => null)) as {
          error?: string
        } | null
        throw new Error(
          payload?.error || 'Failed to create reconciliation link'
        )
      }

      const isFullTransaction =
        Math.abs(allocationValue - maxTransactionAmount) <= AMOUNT_TOLERANCE
      const nextStatus = isFullTransaction
        ? TRANSACTION_STATUS_FULL_MATCH
        : TRANSACTION_STATUS_PARTIAL

      await updateTransactionStatus(
        selection.entityId,
        selectedTransaction.id,
        nextStatus,
        supabase
      )

      setData(prev => ({
        transactions: prev.transactions.filter(
          tx => tx.id !== selectedTransaction.id
        ),
        invoices: prev.invoices.filter(inv => inv.id !== selectedInvoice.id),
      }))
      setSelectedTransactions([])
      setSelectedInvoices([])
      setAllocationAmount('')
      setActionMessage(
        isFullTransaction
          ? 'Transaction and invoice matched successfully.'
          : 'Partial allocation recorded. Remaining balance stays open.'
      )
      setReloadKey(prev => prev + 1)
    } catch (matchError) {
      console.error('Failed to match transaction', matchError)
      setActionError(
        matchError instanceof Error
          ? matchError.message
          : 'Unable to match the selected records.'
      )
    }
  }

  const handleBulkReconcile = async () => {
    if (!selection.entityId || selectedTransactions.length === 0) return
    setActionError(null)
    setActionMessage(null)

    try {
      await Promise.all(
        selectedTransactions.map(transactionId =>
          updateTransactionStatus(
            selection.entityId!,
            transactionId,
            BULK_RECONCILED_STATUS,
            supabase
          )
        )
      )

      setData(prev => ({
        transactions: prev.transactions.filter(
          tx => !selectedTransactions.includes(tx.id)
        ),
        invoices: prev.invoices,
      }))
      setSelectedTransactions([])
      setActionMessage('Selected transactions marked as reconciled.')
      setReloadKey(prev => prev + 1)
    } catch (bulkError) {
      console.error('Failed to reconcile transactions', bulkError)
      setActionError(
        bulkError instanceof Error
          ? bulkError.message
          : 'Unable to mark transactions as reconciled.'
      )
    }
  }

  if (selectionLoading) {
    return (
      <div className="max-w-6xl mx-auto py-16">
        <p className="text-sm text-gray-600">Loading organization details…</p>
      </div>
    )
  }

  if (!isValid || !selection.entityId || !currentEntity) {
    return (
      <div className="max-w-6xl mx-auto py-16">
        <h1 className="text-2xl font-semibold text-gray-900 mb-3">
          Select a company to reconcile transactions
        </h1>
        <p className="text-gray-600">
          Choose a tenant and company from the selector in the top navigation,
          then return to this page to reconcile bank transactions with invoices.
        </p>
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6 py-10">
      <header className="space-y-2">
        <h1 className="text-3xl font-bold text-gray-900">
          Reconcile transactions
        </h1>
        <p className="text-gray-600">
          Pair incoming transactions with outstanding invoices or mark them as
          reconciled in bulk.
        </p>
      </header>

      {(actionMessage || actionError) && (
        <div
          className={`rounded-md border p-4 text-sm ${
            actionError
              ? 'border-red-200 bg-red-50 text-red-700'
              : 'border-green-200 bg-green-50 text-green-700'
          }`}
        >
          {actionError || actionMessage}
        </div>
      )}

      {error && (
        <div className="rounded-md border border-red-200 bg-red-50 p-4 text-sm text-red-700">
          {error}
        </div>
      )}

      <section className="space-y-4 rounded-xl border border-indigo-100 bg-white p-5 shadow-sm">
        <header className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">
              AI suggestions
            </h2>
            <p className="text-xs text-gray-500">
              Suggested matches based on amount, structured references, and
              counterparty history.
            </p>
          </div>
          {suggestionsLoading && (
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <span className="inline-block h-4 w-4 animate-spin rounded-full border-2 border-indigo-500 border-t-transparent"></span>
              Loading…
            </div>
          )}
        </header>

        {suggestions.length === 0 && !suggestionsLoading ? (
          <p className="text-sm text-gray-500">
            No high-confidence matches available right now. Import more
            transactions or refresh after recording matches.
          </p>
        ) : (
          <div className="space-y-3">
            {suggestions.map(suggestion => {
              const reasons = formatSuggestionReasons(suggestion.reason)
              const confidencePercent = Math.round(
                Math.min(suggestion.confidence, 0.99) * 100
              )
              const disabled = suggestionActionId === suggestion.id

              return (
                <div
                  key={suggestion.id}
                  className="rounded-lg border border-indigo-100 bg-indigo-50/60 p-4 shadow-sm"
                >
                  <div className="flex flex-wrap items-start justify-between gap-3">
                    <div>
                      <p className="text-sm font-semibold text-gray-900">
                        {formatCurrency(suggestion.recommended_amount)} →
                        Invoice {suggestion.invoices.number}
                      </p>
                      <p className="text-xs text-gray-600">
                        {suggestion.bank_transactions.counterparty_name ||
                          'Unknown counterparty'}{' '}
                        ·{' '}
                        {formatDate(
                          suggestion.bank_transactions.transaction_date
                        )}
                      </p>
                      <p className="text-xs text-gray-600">
                        Confidence {confidencePercent}%
                      </p>
                      {reasons.length > 0 && (
                        <ul className="mt-2 list-disc pl-4 text-xs text-gray-700">
                          {reasons.map(reason => (
                            <li key={reason}>{reason}</li>
                          ))}
                        </ul>
                      )}
                    </div>
                    <div className="flex flex-col items-end gap-2">
                      <button
                        type="button"
                        onClick={() => {
                          void handleAcceptSuggestion(suggestion)
                        }}
                        disabled={disabled}
                        className="rounded-md border border-indigo-500 px-3 py-1 text-sm text-indigo-600 shadow-sm transition hover:bg-indigo-500 hover:text-white disabled:cursor-not-allowed disabled:border-gray-300 disabled:text-gray-400"
                      >
                        Accept
                      </button>
                      <button
                        type="button"
                        onClick={() => {
                          void handleRejectSuggestion(suggestion)
                        }}
                        disabled={disabled}
                        className="rounded-md border border-gray-300 px-3 py-1 text-xs text-gray-600 shadow-sm transition hover:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-60"
                      >
                        Dismiss
                      </button>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </section>

      <div className="grid gap-6 lg:grid-cols-2">
        <section className="space-y-4 rounded-xl border border-gray-200 bg-white p-5 shadow-sm">
          <header className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                Unmatched transactions
              </h2>
              <p className="text-xs text-gray-500">
                Select one or more bank transactions.
              </p>
            </div>
            {loading && (
              <div className="flex items-center gap-2 text-xs text-gray-500">
                <span className="inline-block h-4 w-4 animate-spin rounded-full border-2 border-indigo-500 border-t-transparent"></span>
                Loading…
              </div>
            )}
          </header>

          <input
            type="search"
            placeholder="Search transactions"
            value={transactionSearch}
            onChange={event => setTransactionSearch(event.target.value)}
            className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
          />

          <div className="max-h-[28rem] space-y-2 overflow-y-auto pr-1">
            {filteredTransactions.length === 0 ? (
              <p className="text-sm text-gray-500">
                No transactions awaiting reconciliation.
              </p>
            ) : (
              filteredTransactions.map(tx => {
                const isSelected = selectedTransactions.includes(tx.id)
                return (
                  <button
                    type="button"
                    key={tx.id}
                    onClick={() => handleTransactionSelect(tx.id)}
                    className={`w-full rounded-lg border px-3 py-3 text-left shadow-sm transition ${
                      isSelected
                        ? 'border-indigo-400 bg-indigo-50'
                        : 'border-gray-200 bg-white hover:border-indigo-200 hover:bg-indigo-50'
                    }`}
                  >
                    <div className="flex items-center justify-between text-sm font-semibold text-gray-900">
                      <span>{formatDate(tx.transaction_date)}</span>
                      <span
                        className={
                          tx.amount < 0 ? 'text-red-600' : 'text-gray-900'
                        }
                      >
                        {formatCurrency(tx.amount)}
                      </span>
                    </div>
                    <p className="mt-1 text-sm text-gray-700">
                      {tx.description || tx.reference || 'No description'}
                    </p>
                    {tx.counterparty_name && (
                      <p className="text-xs text-gray-500">
                        {tx.counterparty_name}
                      </p>
                    )}
                    {tx.structured_ref && (
                      <p className="text-xs font-mono text-gray-500">
                        {tx.structured_ref}
                      </p>
                    )}
                  </button>
                )
              })
            )}
          </div>

          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600">
              {selectedTransactions.length} selected
            </span>
            <button
              type="button"
              onClick={() => {
                void handleBulkReconcile()
              }}
              disabled={selectedTransactions.length === 0 || loading}
              className="rounded-md border border-gray-300 px-3 py-1 text-sm text-gray-700 shadow-sm disabled:cursor-not-allowed disabled:opacity-50"
            >
              Mark as reconciled
            </button>
          </div>
        </section>

        <section className="space-y-4 rounded-xl border border-gray-200 bg-white p-5 shadow-sm">
          <header className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                Open invoices
              </h2>
              <p className="text-xs text-gray-500">
                Select a single invoice to match.
              </p>
            </div>
          </header>

          <input
            type="search"
            placeholder="Search invoices"
            value={invoiceSearch}
            onChange={event => setInvoiceSearch(event.target.value)}
            className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
          />

          <div className="max-h-[28rem] space-y-2 overflow-y-auto pr-1">
            {filteredInvoices.length === 0 ? (
              <p className="text-sm text-gray-500">
                No outstanding invoices found.
              </p>
            ) : (
              filteredInvoices.map(inv => {
                const isSelected = selectedInvoices.includes(inv.id)
                return (
                  <button
                    type="button"
                    key={inv.id}
                    onClick={() => handleInvoiceSelect(inv.id)}
                    className={`w-full rounded-lg border px-3 py-3 text-left shadow-sm transition ${
                      isSelected
                        ? 'border-indigo-400 bg-indigo-50'
                        : 'border-gray-200 bg-white hover:border-indigo-200 hover:bg-indigo-50'
                    }`}
                  >
                    <div className="flex items-center justify-between text-sm font-semibold text-gray-900">
                      <span>{inv.number}</span>
                      <span>{formatCurrency(inv.total_amount)}</span>
                    </div>
                    <p className="mt-1 text-sm text-gray-700">
                      {inv.counterparty_name}
                    </p>
                    <p className="text-xs text-gray-500">
                      Issued {formatDate(inv.invoice_date)}
                      {inv.due_date ? ` · Due ${formatDate(inv.due_date)}` : ''}
                    </p>
                  </button>
                )
              })
            )}
          </div>

          <div>
            <label
              className="block text-xs font-medium text-gray-600"
              htmlFor="allocation-amount"
            >
              Amount to allocate
            </label>
            <input
              id="allocation-amount"
              type="number"
              step="0.01"
              min="0"
              value={allocationAmount}
              onChange={event => setAllocationAmount(event.target.value)}
              className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
            />
            <p className="mt-1 text-xs text-gray-500">
              Defaults to the smallest of the transaction and invoice totals.
              Adjust for partial allocations.
            </p>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600">
              {selectedInvoices.length} selected
            </span>
            <button
              type="button"
              onClick={() => {
                void handleMatch()
              }}
              disabled={!canMatch || loading}
              className="rounded-md border border-indigo-500 px-3 py-1 text-sm text-indigo-600 shadow-sm transition disabled:cursor-not-allowed disabled:border-gray-300 disabled:text-gray-400"
            >
              {matchButtonLabel}
            </button>
          </div>
          {allocationTooLargeForInvoice && (
            <p className="text-xs text-red-600">
              Allocation cannot exceed the outstanding invoice balance.
            </p>
          )}
          {!amountsAligned && canMatch && !allocationTooLargeForInvoice && (
            <p className="text-xs text-gray-500">
              Partial allocation recorded will keep the remaining transaction
              balance unmatched.
            </p>
          )}
        </section>
      </div>
    </div>
  )
}

async function fetchTransactions(
  entityId: number,
  _supabase: ReturnType<typeof createSecureBrowserClient>
): Promise<TransactionRow[]> {
  const params = new URLSearchParams({
    limit: String(MATCH_LIMIT),
    offset: '0',
    status: 'unmatched',
  })

  const response = await fetch(
    `/api/entities/${entityId}/bank-transactions?${params.toString()}`
  )

  if (!response.ok) {
    const payload = (await response.json().catch(() => null)) as {
      error?: string
    } | null
    throw new Error(payload?.error || 'Failed to load transactions')
  }

  const payload = (await response.json()) as { data?: unknown }
  const rows = (payload.data as TransactionRow[]) ?? []
  return rows
}

async function fetchInvoices(
  entityId: number,
  supabase: ReturnType<typeof createSecureBrowserClient>
): Promise<InvoiceRow[]> {
  const { data, error } = await supabase
    .from('invoices')
    .select(
      'id, invoice_date, due_date, total_amount, status, kind, number, counterparty_name'
    )
    .eq('entity_id', entityId)
    .not('status', 'in', '("paid","cancelled")')
    .order('invoice_date', { ascending: false })
    .limit(MATCH_LIMIT)

  if (error) {
    throw new Error(error.message)
  }

  type SupabaseInvoiceRow = {
    id: number
    invoice_date: string
    due_date: string | null
    total_amount: number
    status: string
    kind: string
    number: string
    counterparty_name: string
  }

  const rows = (data ?? []) as SupabaseInvoiceRow[]

  return rows
    .filter(invoice => invoice.kind === 'sale' || invoice.kind === 'purchase')
    .map(invoice => ({
      id: invoice.id,
      invoice_date: invoice.invoice_date,
      due_date: invoice.due_date,
      total_amount: Number(invoice.total_amount),
      status: invoice.status,
      kind: invoice.kind as 'sale' | 'purchase',
      number: invoice.number,
      counterparty_name: invoice.counterparty_name,
    }))
}

async function updateTransactionStatus(
  entityId: number,
  transactionId: number,
  status: string,
  supabase: ReturnType<typeof createSecureBrowserClient>
) {
  await supabase.auth.getSession()

  const response = await fetch(
    `/api/entities/${entityId}/bank-transactions/${transactionId}/status`,
    {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ status }),
    }
  )

  if (!response.ok) {
    const payload = (await response.json().catch(() => null)) as {
      error?: string
    } | null
    throw new Error(payload?.error || 'Failed to update transaction status')
  }
}

function normalizeSuggestion(raw: unknown): MatchSuggestion | null {
  if (!raw || typeof raw !== 'object') return null
  const suggestion = raw as Record<string, unknown>
  const transaction = suggestion.bank_transactions as
    | Record<string, unknown>
    | undefined
  const invoice = suggestion.invoices as Record<string, unknown> | undefined

  if (!transaction || !invoice) return null

  const tx: TransactionRow = {
    id: Number(transaction.id ?? 0),
    bank_account_id: Number(transaction.bank_account_id ?? 0),
    transaction_date: String(transaction.transaction_date ?? ''),
    value_date: (transaction.value_date as string | null) ?? null,
    amount: Number(transaction.amount ?? 0),
    description: (transaction.description as string | null) ?? null,
    reference: (transaction.reference as string | null) ?? null,
    counterparty_name: (transaction.counterparty_name as string | null) ?? null,
    counterparty_account:
      (transaction.counterparty_account as string | null) ?? null,
    structured_ref: (transaction.structured_ref as string | null) ?? null,
  }

  const inv: InvoiceRow = {
    id: Number(invoice.id ?? 0),
    invoice_date: String(invoice.invoice_date ?? ''),
    due_date: (invoice.due_date as string | null) ?? null,
    total_amount: Number(invoice.total_amount ?? 0),
    status: String(invoice.status ?? ''),
    kind: invoice.kind === 'purchase' ? 'purchase' : 'sale',
    number: String(invoice.number ?? ''),
    counterparty_name: String(invoice.counterparty_name ?? ''),
  }

  if (!tx.id || !inv.id) return null

  return {
    id: Number(suggestion.id ?? 0),
    confidence: Number(suggestion.confidence ?? 0),
    recommended_amount: Number(
      suggestion.recommended_amount ?? Math.abs(tx.amount)
    ),
    reason: (suggestion.reason as Record<string, unknown> | null) ?? null,
    bank_transactions: tx,
    invoices: inv,
  }
}

function formatSuggestionReasons(
  reason: Record<string, unknown> | null
): string[] {
  if (!reason) return ['Exact amount match']
  const items: string[] = []
  if (truthy(reason.amountMatch)) {
    items.push('Exact amount match')
  }
  if (truthy(reason.structuredRefMatch)) {
    items.push('Structured reference matches invoice number')
  }
  if (truthy(reason.counterpartyMatch)) {
    items.push('Counterparty names align')
  }
  if (typeof reason.dateDiffDays === 'number') {
    items.push(
      `Transaction date ${Math.abs(reason.dateDiffDays)} day(s) from invoice date`
    )
  }
  return items.length ? items : ['Exact amount match']
}

function truthy(value: unknown): boolean {
  if (typeof value === 'boolean') return value
  if (typeof value === 'number') return value !== 0
  if (typeof value === 'string') return value.toLowerCase() === 'true'
  return Boolean(value)
}

function formatCurrency(value: number): string {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'EUR',
  })
  return formatter.format(value)
}

function formatDate(value: string | null): string {
  if (!value) return '—'
  const date = new Date(value)
  if (Number.isNaN(date.getTime())) return value
  return date.toLocaleDateString()
}
