'use client'

import { useEffect, useMemo, useState } from 'react'
import { useOrgEntitySelection } from '@/hooks/useOrgEntitySelection'
import { createSecureBrowserClient } from '@/lib/session-security'

const PAGE_SIZE = 25

const STATUS_OPTIONS = [
  'unmatched',
  'proposed',
  'matched',
  'reconciled',
  'failed',
] as const

type StatusOption = (typeof STATUS_OPTIONS)[number]

type BankTransactionRow = {
  id: number
  transaction_date: string
  value_date: string | null
  amount: number
  counterparty_name: string | null
  counterparty_account: string | null
  description: string | null
  reference: string | null
  status: StatusOption
  currency: string | null
  structured_ref: string | null
  bank_accounts: {
    id: number
    name: string | null
  } | null
}

type BankAccountOption = {
  id: number
  name: string | null
}

type FilterState = {
  status: '' | StatusOption
  fromDate: string
  toDate: string
  minAmount: string
  maxAmount: string
  bankAccountId: string
  search: string
}

const initialFilters: FilterState = {
  status: '',
  fromDate: '',
  toDate: '',
  minAmount: '',
  maxAmount: '',
  bankAccountId: '',
  search: '',
}

export default function TransactionsPage() {
  const {
    selection,
    currentEntity,
    loading: selectionLoading,
    isValid,
  } = useOrgEntitySelection()
  const supabase = useMemo(() => createSecureBrowserClient(), [])

  const [accounts, setAccounts] = useState<BankAccountOption[]>([])
  const [accountsLoading, setAccountsLoading] = useState(false)

  const [filters, setFilters] = useState<FilterState>(initialFilters)
  const [appliedFilters, setAppliedFilters] =
    useState<FilterState>(initialFilters)
  const [page, setPage] = useState(1)

  const [transactions, setTransactions] = useState<BankTransactionRow[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState(false)

  useEffect(() => {
    if (!selection.entityId) {
      setAccounts([])
      return
    }

    const entityId = selection.entityId

    let cancelled = false
    const loadAccounts = async (id: number) => {
      setAccountsLoading(true)
      const { data, error: fetchError } = await supabase
        .from('bank_accounts')
        .select('id, bank_name, account_number, iban')
        .eq('entity_id', id)
        .order('bank_name', { ascending: true })

      if (!cancelled) {
        if (fetchError) {
          console.error('Failed to fetch bank accounts', fetchError)
          setAccounts([])
        } else {
          const normalized = (data ?? []).map(account => ({
            id: account.id,
            name:
              account.bank_name ??
              account.account_number ??
              account.iban ??
              null,
          }))
          setAccounts(normalized)
        }
        setAccountsLoading(false)
      }
    }

    void loadAccounts(entityId)

    return () => {
      cancelled = true
    }
  }, [selection.entityId, supabase])

  useEffect(() => {
    const timeout = setTimeout(() => {
      setAppliedFilters(filters)
      setPage(1)
    }, 400)
    return () => clearTimeout(timeout)
  }, [filters])

  useEffect(() => {
    if (!selection.entityId) {
      setTransactions([])
      return
    }

    const entityId = selection.entityId
    let cancelled = false

    const fetchTransactions = async () => {
      try {
        setLoading(true)
        setError(null)

        const params = new URLSearchParams()
        params.set('limit', String(PAGE_SIZE))
        params.set('offset', String((page - 1) * PAGE_SIZE))

        if (appliedFilters.status) params.set('status', appliedFilters.status)
        if (appliedFilters.fromDate)
          params.set('from_date', appliedFilters.fromDate)
        if (appliedFilters.toDate) params.set('to_date', appliedFilters.toDate)
        if (appliedFilters.bankAccountId)
          params.set('bank_account_id', appliedFilters.bankAccountId)
        if (appliedFilters.minAmount)
          params.set('min_amount', appliedFilters.minAmount)
        if (appliedFilters.maxAmount)
          params.set('max_amount', appliedFilters.maxAmount)
        if (appliedFilters.search) params.set('search', appliedFilters.search)

        const response = await fetch(
          `/api/entities/${entityId}/bank-transactions?${params.toString()}`,
          {
            method: 'GET',
          }
        )

        if (!response.ok) {
          const payload = (await response.json().catch(() => null)) as {
            error?: string
          } | null
          throw new Error(
            payload?.error ||
              `Failed to fetch transactions (status ${response.status})`
          )
        }

        const payload = (await response.json()) as {
          success: boolean
          data?: unknown
        }
        const data = (payload.data as BankTransactionRow[]) ?? []

        if (!cancelled) {
          setTransactions(data)
          setHasMore(data.length === PAGE_SIZE)
        }
      } catch (fetchError) {
        if (!cancelled) {
          console.error('Failed to load transactions', fetchError)
          setError(
            fetchError instanceof Error
              ? fetchError.message
              : 'Unable to load transactions'
          )
          setTransactions([])
        }
      } finally {
        if (!cancelled) setLoading(false)
      }
    }

    void fetchTransactions()

    return () => {
      cancelled = true
    }
  }, [appliedFilters, page, selection.entityId, supabase])

  const formatAmount = (amount: number, currency?: string | null) => {
    const value = Number.isFinite(amount) ? amount : 0
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'EUR',
    })
    return formatter.format(value)
  }

  const statusBadgeClasses = useMemo<Record<StatusOption, string>>(
    () => ({
      unmatched: 'bg-gray-100 text-gray-700',
      proposed: 'bg-blue-100 text-blue-700',
      matched: 'bg-green-100 text-green-700',
      reconciled: 'bg-emerald-100 text-emerald-700',
      failed: 'bg-red-100 text-red-700',
    }),
    []
  )

  if (selectionLoading) {
    return (
      <div className="max-w-6xl mx-auto py-16">
        <p className="text-sm text-gray-600">Loading organization details…</p>
      </div>
    )
  }

  if (!isValid || !selection.entityId || !currentEntity) {
    return (
      <div className="max-w-6xl mx-auto py-16">
        <h1 className="text-2xl font-semibold text-gray-900 mb-3">
          Select a company to view transactions
        </h1>
        <p className="text-gray-600">
          Choose a tenant and company from the selector in the top navigation,
          then return to this page to manage bank transactions.
        </p>
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto py-10 space-y-8">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold text-gray-900">Bank transactions</h1>
        <p className="text-gray-600">
          Review imported transactions, filter by status or date, and drill into
          the records before reconciliation.
        </p>
      </div>

      <section className="space-y-4 rounded-xl border border-gray-200 bg-white p-5 shadow-sm">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <div>
            <label
              className="block text-xs font-medium text-gray-600"
              htmlFor="search"
            >
              Search
            </label>
            <input
              id="search"
              type="search"
              placeholder="Description, counterparty, reference…"
              value={filters.search}
              onChange={event =>
                setFilters(prev => ({ ...prev, search: event.target.value }))
              }
              className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
            />
          </div>

          <div>
            <label
              className="block text-xs font-medium text-gray-600"
              htmlFor="status-filter"
            >
              Status
            </label>
            <select
              id="status-filter"
              value={filters.status}
              onChange={event =>
                setFilters(prev => ({
                  ...prev,
                  status: event.target.value as FilterState['status'],
                }))
              }
              className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
            >
              <option value="">All statuses</option>
              {STATUS_OPTIONS.map(option => (
                <option key={option} value={option}>
                  {titleCase(option)}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label
              className="block text-xs font-medium text-gray-600"
              htmlFor="account-filter"
            >
              Bank account
            </label>
            <select
              id="account-filter"
              value={filters.bankAccountId}
              onChange={event =>
                setFilters(prev => ({
                  ...prev,
                  bankAccountId: event.target.value,
                }))
              }
              className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
            >
              <option value="">All accounts</option>
              {accounts.map(account => (
                <option key={account.id} value={account.id}>
                  {account.name || `Account ${account.id}`}
                </option>
              ))}
            </select>
            {accountsLoading && (
              <p className="mt-1 text-xs text-gray-500">Loading accounts…</p>
            )}
          </div>

          <div>
            <label
              className="block text-xs font-medium text-gray-600"
              htmlFor="from-date"
            >
              From date
            </label>
            <input
              id="from-date"
              type="date"
              value={filters.fromDate}
              onChange={event =>
                setFilters(prev => ({ ...prev, fromDate: event.target.value }))
              }
              className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
            />
          </div>

          <div>
            <label
              className="block text-xs font-medium text-gray-600"
              htmlFor="to-date"
            >
              To date
            </label>
            <input
              id="to-date"
              type="date"
              value={filters.toDate}
              onChange={event =>
                setFilters(prev => ({ ...prev, toDate: event.target.value }))
              }
              className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
            />
          </div>

          <div className="grid grid-cols-2 gap-3">
            <div>
              <label
                className="block text-xs font-medium text-gray-600"
                htmlFor="min-amount"
              >
                Min amount
              </label>
              <input
                id="min-amount"
                type="number"
                inputMode="decimal"
                value={filters.minAmount}
                onChange={event =>
                  setFilters(prev => ({
                    ...prev,
                    minAmount: event.target.value,
                  }))
                }
                className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
              />
            </div>
            <div>
              <label
                className="block text-xs font-medium text-gray-600"
                htmlFor="max-amount"
              >
                Max amount
              </label>
              <input
                id="max-amount"
                type="number"
                inputMode="decimal"
                value={filters.maxAmount}
                onChange={event =>
                  setFilters(prev => ({
                    ...prev,
                    maxAmount: event.target.value,
                  }))
                }
                className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
              />
            </div>
          </div>
        </div>
      </section>

      <section className="rounded-xl border border-gray-200 bg-white shadow-sm">
        <header className="flex items-center justify-between border-b border-gray-200 p-4">
          <div>
            <p className="text-sm font-medium text-gray-700">
              {transactions.length} transaction
              {transactions.length === 1 ? '' : 's'} on this page
            </p>
            {appliedFilters.search && (
              <p className="text-xs text-gray-500">
                Search term: “{appliedFilters.search}”
              </p>
            )}
          </div>
          {loading && (
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <span className="inline-block h-4 w-4 animate-spin rounded-full border-2 border-indigo-500 border-t-transparent"></span>
              Loading…
            </div>
          )}
        </header>

        {error ? (
          <div className="p-6 text-sm text-red-600">{error}</div>
        ) : transactions.length === 0 && !loading ? (
          <div className="p-6 text-sm text-gray-600">
            No transactions found for the selected filters.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50 text-xs uppercase text-gray-500">
                <tr>
                  <th className="px-4 py-3 text-left">Booking date</th>
                  <th className="px-4 py-3 text-left">Value date</th>
                  <th className="px-4 py-3 text-left">Description</th>
                  <th className="px-4 py-3 text-left">Counterparty</th>
                  <th className="px-4 py-3 text-left">Account</th>
                  <th className="px-4 py-3 text-right">Amount</th>
                  <th className="px-4 py-3 text-center">Status</th>
                </tr>
              </thead>
              <tbody className="bg-white text-sm text-gray-800">
                {transactions.map(tx => (
                  <tr
                    key={tx.id}
                    className="border-b border-gray-100 last:border-0"
                  >
                    <td className="px-4 py-3 align-top font-medium text-gray-900">
                      {formatDate(tx.transaction_date)}
                    </td>
                    <td className="px-4 py-3 align-top text-gray-600">
                      {tx.value_date ? formatDate(tx.value_date) : '—'}
                    </td>
                    <td className="px-4 py-3 align-top text-gray-700">
                      <p className="font-medium text-gray-900">
                        {tx.description || '—'}
                      </p>
                      {tx.reference && (
                        <p className="text-xs text-gray-500">{tx.reference}</p>
                      )}
                      {tx.structured_ref && (
                        <p className="text-xs font-mono text-gray-500">
                          {tx.structured_ref}
                        </p>
                      )}
                    </td>
                    <td className="px-4 py-3 align-top text-gray-700">
                      <p>{tx.counterparty_name || '—'}</p>
                      {tx.counterparty_account && (
                        <p className="text-xs text-gray-500">
                          {tx.counterparty_account}
                        </p>
                      )}
                    </td>
                    <td className="px-4 py-3 align-top text-gray-700">
                      {tx.bank_accounts?.name ||
                        `Account ${tx.bank_accounts?.id ?? '—'}`}
                    </td>
                    <td className="px-4 py-3 text-right align-top font-semibold">
                      <span
                        className={
                          tx.amount < 0 ? 'text-red-600' : 'text-gray-900'
                        }
                      >
                        {formatAmount(tx.amount, tx.currency)}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-center align-top">
                      <span
                        className={`inline-flex items-center rounded-full px-3 py-1 text-xs font-semibold ${statusBadgeClasses[tx.status]}`}
                      >
                        {titleCase(tx.status)}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        <footer className="flex items-center justify-between border-t border-gray-200 p-4 text-sm">
          <span className="text-gray-600">Page {page}</span>
          <div className="flex items-center gap-3">
            <button
              type="button"
              onClick={() => setPage(prev => Math.max(1, prev - 1))}
              disabled={page === 1 || loading}
              className="rounded-md border border-gray-300 px-3 py-1 text-sm text-gray-700 shadow-sm disabled:cursor-not-allowed disabled:opacity-50"
            >
              Previous
            </button>
            <button
              type="button"
              onClick={() => setPage(prev => (hasMore ? prev + 1 : prev))}
              disabled={!hasMore || loading}
              className="rounded-md border border-gray-300 px-3 py-1 text-sm text-gray-700 shadow-sm disabled:cursor-not-allowed disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </footer>
      </section>
    </div>
  )
}

function titleCase(value: string): string {
  return value
    .replace(/[_-]/g, ' ')
    .replace(/\b\w/g, match => match.toUpperCase())
    .trim()
}

function formatDate(value: string): string {
  const date = new Date(value)
  if (Number.isNaN(date.getTime())) return value
  return date.toLocaleDateString()
}
