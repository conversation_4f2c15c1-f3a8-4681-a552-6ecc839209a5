/**
 * Secure Bank Import API Route
 * Handles bank transaction imports with proper server-side authentication
 */

/* eslint-disable @typescript-eslint/no-unsafe-assignment */

import { NextRequest } from 'next/server'
import { createSecureServerClient } from '@/lib/supabase-server'
import { createJsonResponse } from '@/lib/http/createJsonResponse'
import { BFF_CONFIG, createBFFHeadersNoContent } from '@/lib/bff-config'

export async function POST(
  request: NextRequest,
  { params }: { params: { entityId: string } }
): Promise<Response> {
  try {
    // Authenticate user server-side
    const supabase = createSecureServerClient()
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return createJsonResponse({ error: 'Unauthorized' }, 401)
    }

    // Get user session for token
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession()

    if (sessionError || !session) {
      return createJsonResponse({ error: 'No valid session' }, 401)
    }

    // Validate entity ID
    const entityId = parseInt(params.entityId, 10)
    if (isNaN(entityId)) {
      return createJsonResponse({ error: 'Invalid entity ID' }, 400)
    }

    // Verify user has access to this entity
    const { data: entityAccess, error: entityError } = await supabase
      .from('v_user_entities')
      .select('entity_id, role')
      .eq('entity_id', entityId)
      .in('role', ['owner', 'admin', 'accountant', 'bookkeeper'])
      .single()

    if (entityError || !entityAccess) {
      return createJsonResponse(
        {
          error: 'Access denied or insufficient permissions for entity',
        },
        403
      )
    }

    // Forward the request to BFF with proper authentication
    const formData = await request.formData()

    const bffResponse = await fetch(
      `${BFF_CONFIG.baseUrl}/entities/${entityId}/bank-transactions/import`,
      {
        method: 'POST',
        headers: createBFFHeadersNoContent(session.access_token),
        body: formData,
      }
    )

    // Forward the response from BFF
    if (!bffResponse.ok) {
      const errorText = await bffResponse.text()
      let errorData
      try {
        errorData = JSON.parse(errorText)
      } catch {
        errorData = {
          error: `BFF request failed with status ${bffResponse.status}`,
        }
      }

      return createJsonResponse(errorData, bffResponse.status)
    }

    const responseData = await bffResponse.json()
    return createJsonResponse(responseData, 200)
  } catch (error) {
    console.error('Bank import API error:', error)
    return createJsonResponse({ error: 'Internal server error' }, 500)
  }
}
