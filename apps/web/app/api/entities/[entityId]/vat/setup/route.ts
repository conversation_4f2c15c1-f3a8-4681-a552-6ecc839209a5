import { NextRequest } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { createJsonResponse } from '@/lib/http/createJsonResponse'
import { saveVATConfiguration, getVATConfiguration } from '@belbooks/dal'
import type { Database } from '@belbooks/types'
import { VATSetupRequestSchema } from '@belbooks/types'

export async function POST(
  request: NextRequest,
  { params }: { params: { entityId: string } }
): Promise<Response> {
  try {
    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Check authentication
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()
    if (authError || !user) {
      return createJsonResponse({ error: 'Unauthorized' }, 401)
    }

    // Parse and validate request body
    const body: unknown = await request.json()
    const vatSetupRequest = VATSetupRequestSchema.parse(body)

    const entityId = parseInt(params.entityId, 10)
    if (isNaN(entityId)) {
      return createJsonResponse({ error: 'Invalid entity ID' }, 400)
    }

    // Save VAT configuration using typed DAL function
    const result = await saveVATConfiguration(
      supabase,
      entityId,
      vatSetupRequest
    )

    if (!result.success) {
      return createJsonResponse(
        { error: result.error || 'Failed to save VAT configuration' },
        400
      )
    }

    return createJsonResponse(
      {
        success: true,
        data: result,
      },
      200
    )
  } catch (error: unknown) {
    console.error('VAT setup API error:', error)

    // Handle validation errors
    if (error && typeof error === 'object' && 'issues' in error) {
      const validationError = error as { issues: Array<{ message: string }> }
      return createJsonResponse(
        {
          error: `Validation error: ${validationError.issues
            .map(issue => issue.message)
            .join(', ')}`,
        },
        400
      )
    }

    const message =
      error instanceof Error ? error.message : 'Internal server error'
    return createJsonResponse({ error: message }, 500)
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { entityId: string } }
): Promise<Response> {
  try {
    // Create Supabase client
    const cookieStore = cookies()
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Check authentication
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()
    if (authError || !user) {
      return createJsonResponse({ error: 'Unauthorized' }, 401)
    }

    const entityId = parseInt(params.entityId, 10)
    if (isNaN(entityId)) {
      return createJsonResponse({ error: 'Invalid entity ID' }, 400)
    }

    // Get current VAT configuration
    const configuration = await getVATConfiguration(supabase, entityId)

    return createJsonResponse(
      {
        success: true,
        data: { configuration },
      },
      200
    )
  } catch (error: unknown) {
    console.error('VAT configuration retrieval API error:', error)

    const message =
      error instanceof Error ? error.message : 'Internal server error'
    return createJsonResponse({ error: message }, 500)
  }
}
