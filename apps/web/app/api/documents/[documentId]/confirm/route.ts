/**
 * Secure Document Confirmation API Route
 * Handles document confirmation with proper server-side authentication
 */

/* eslint-disable @typescript-eslint/no-unsafe-assignment */

import { NextRequest } from 'next/server'
import { createSecureServerClient } from '@/lib/supabase-server'
import { createJsonResponse } from '@/lib/http/createJsonResponse'
import { BFF_CONFIG, createBFFHeaders } from '@/lib/bff-config'

export async function POST(
  request: NextRequest,
  { params }: { params: { documentId: string } }
): Promise<Response> {
  try {
    // Authenticate user server-side
    const supabase = createSecureServerClient()
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return createJsonResponse({ error: 'Unauthorized' }, 401)
    }

    // Get user session for token
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession()

    if (sessionError || !session) {
      return createJsonResponse({ error: 'No valid session' }, 401)
    }

    // Validate document ID
    const documentId = parseInt(params.documentId, 10)
    if (isNaN(documentId)) {
      return createJsonResponse({ error: 'Invalid document ID' }, 400)
    }

    // Verify user has access to this document through entity membership
    const { data: documentAccess, error: documentError } = await supabase
      .from('inbox_documents')
      .select(
        `
        id,
        entity_id,
        entities!inner (
          id,
          entity_memberships!inner (
            user_id,
            role
          )
        )
      `
      )
      .eq('id', documentId)
      .eq('entities.entity_memberships.user_id', user.id)
      .in('entities.entity_memberships.role', [
        'owner',
        'admin',
        'accountant',
        'bookkeeper',
      ])
      .single()

    if (documentError || !documentAccess) {
      return createJsonResponse(
        {
          error: 'Document not found or insufficient permissions',
        },
        404
      )
    }

    // Get request body
    const requestBody = await request.json()

    // Forward the request to BFF with proper authentication
    const bffResponse = await fetch(
      `${BFF_CONFIG.baseUrl}/documents/${documentId}/confirm`,
      {
        method: 'POST',
        headers: createBFFHeaders(session.access_token),
        body: JSON.stringify(requestBody),
      }
    )

    // Forward the response from BFF
    if (!bffResponse.ok) {
      const errorText = await bffResponse.text()
      let errorData
      try {
        errorData = JSON.parse(errorText)
      } catch {
        errorData = {
          error: `BFF request failed with status ${bffResponse.status}`,
        }
      }

      return createJsonResponse(errorData, bffResponse.status)
    }

    const responseData = await bffResponse.json()
    return createJsonResponse(responseData, 200)
  } catch (error) {
    console.error('Document confirmation API error:', error)
    return createJsonResponse({ error: 'Internal server error' }, 500)
  }
}
