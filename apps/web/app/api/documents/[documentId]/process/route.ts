/**
 * Secure Document Processing API Route
 * Handles document processing with proper server-side authentication
 */

/* eslint-disable @typescript-eslint/no-unsafe-assignment */

import { NextRequest } from 'next/server'
import { createSecureServerClient } from '@/lib/supabase-server'
import { createJsonResponse } from '@/lib/http/createJsonResponse'
import { BFF_CONFIG, createBFFHeaders } from '@/lib/bff-config'

export async function POST(
  request: NextRequest,
  { params }: { params: { documentId: string } }
): Promise<Response> {
  try {
    // Authenticate user server-side
    const supabase = createSecureServerClient()
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return createJsonResponse({ error: 'Unauthorized' }, 401)
    }

    // Get user session for token
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession()

    if (sessionError || !session) {
      return createJsonResponse({ error: 'No valid session' }, 401)
    }

    // Validate document ID
    const documentId = parseInt(params.documentId, 10)
    if (isNaN(documentId)) {
      return createJsonResponse({ error: 'Invalid document ID' }, 400)
    }

    // Verify user has access to this document
    const { data: document, error: docError } = await supabase
      .from('documents')
      .select('id, entity_id')
      .eq('id', documentId)
      .single()

    if (docError || !document) {
      return createJsonResponse(
        {
          error: 'Document not found or access denied',
        },
        404
      )
    }

    // Verify user has access to the entity
    const { data: entityAccess, error: entityError } = await supabase
      .from('v_user_entities')
      .select('entity_id, role')
      .eq('entity_id', document.entity_id)
      .in('role', ['owner', 'admin', 'accountant', 'bookkeeper'])
      .single()

    if (entityError || !entityAccess) {
      return createJsonResponse(
        {
          error: 'Access denied or insufficient permissions for entity',
        },
        403
      )
    }

    // Parse request body (if any)
    let body = {}
    try {
      body = await request.json()
    } catch {
      // No body is fine for this endpoint
    }

    // Forward the request to BFF with proper authentication
    const bffResponse = await fetch(
      `${BFF_CONFIG.baseUrl}/documents/${documentId}/process`,
      {
        method: 'POST',
        headers: createBFFHeaders(session.access_token),
        body: JSON.stringify(body),
      }
    )

    // Forward the response from BFF
    if (!bffResponse.ok) {
      const errorText = await bffResponse.text()
      let errorData
      try {
        errorData = JSON.parse(errorText)
      } catch {
        errorData = {
          error: `BFF request failed with status ${bffResponse.status}`,
        }
      }

      return createJsonResponse(errorData, bffResponse.status)
    }

    const responseData = await bffResponse.json()
    return createJsonResponse(responseData, 200)
  } catch (error) {
    console.error('Document processing API error:', error)
    return createJsonResponse({ error: 'Internal server error' }, 500)
  }
}
