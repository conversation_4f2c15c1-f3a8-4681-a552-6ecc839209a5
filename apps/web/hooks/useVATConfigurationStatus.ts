import { useCallback, useEffect, useRef, useState } from 'react'
import type { VATConfiguration } from '@belbooks/types'
import { useOrgEntitySelection } from '@/hooks/useOrgEntitySelection'

interface VATConfigurationStatus {
  configuration: VATConfiguration | null
  isConfigured: boolean
  loading: boolean
  error: string | null
  refresh: () => Promise<VATConfiguration | null>
}

const unexpectedResponseError = 'Unexpected VAT configuration response'

export const useVATConfigurationStatus = (): VATConfigurationStatus => {
  const { currentEntity } = useOrgEntitySelection()
  const [configuration, setConfiguration] = useState<VATConfiguration | null>(
    null
  )
  const [isConfigured, setIsConfigured] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const isMountedRef = useRef(true)

  const fetchConfiguration =
    useCallback(async (): Promise<VATConfiguration | null> => {
      const entityId = currentEntity?.entity_id

      if (!entityId) {
        if (isMountedRef.current) {
          setConfiguration(null)
          setIsConfigured(false)
          setLoading(false)
          setError(null)
        }
        return null
      }

      if (isMountedRef.current) {
        setLoading(true)
        setError(null)
      }

      try {
        const response = await fetch(`/api/entities/${entityId}/vat/setup`)

        if (!response.ok) {
          throw new Error(
            `Failed to load VAT configuration (${response.status})`
          )
        }

        const result: unknown = await response.json()

        if (
          typeof result !== 'object' ||
          result === null ||
          !('success' in result)
        ) {
          throw new Error(unexpectedResponseError)
        }

        const typedResult = result as {
          success: boolean
          data?: { configuration?: VATConfiguration }
          error?: string
        }

        if (!typedResult.success) {
          throw new Error(
            typedResult.error || 'Failed to load VAT configuration details'
          )
        }

        const config = typedResult.data?.configuration ?? null

        if (isMountedRef.current) {
          setConfiguration(config)
          setIsConfigured(Boolean(config?.enabled))
        }

        return config
      } catch (err) {
        if (!isMountedRef.current) {
          return null
        }

        if (err instanceof DOMException && err.name === 'AbortError') {
          return null
        }

        setConfiguration(null)
        setIsConfigured(false)
        setError(
          err instanceof Error
            ? err.message
            : 'Failed to load VAT configuration'
        )
        return null
      } finally {
        if (isMountedRef.current) {
          setLoading(false)
        }
      }
    }, [currentEntity?.entity_id])

  useEffect(() => {
    isMountedRef.current = true

    void fetchConfiguration()

    return () => {
      isMountedRef.current = false
    }
  }, [fetchConfiguration])

  const refresh = useCallback(async () => {
    return await fetchConfiguration()
  }, [fetchConfiguration])

  return {
    configuration,
    isConfigured,
    loading,
    error,
    refresh,
  }
}
