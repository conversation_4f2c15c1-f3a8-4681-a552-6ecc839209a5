'use client'

import { useEffect, useState } from 'react'

/**
 * Hook to get CSRF token from meta tags
 */
export function useCSRF() {
  const [csrfToken, setCSRFToken] = useState<string | null>(null)

  useEffect(() => {
    // Get CSRF token from meta tag
    const metaElement = document.querySelector('meta[name="csrf-token"]')
    if (metaElement) {
      setCSRFToken(metaElement.getAttribute('content'))
    }
  }, [])

  /**
   * Add CSRF token to fetch headers
   */
  const addCSRFHeaders = (
    headers: Record<string, string> = {}
  ): Record<string, string> => {
    if (csrfToken) {
      return {
        ...headers,
        'x-csrf-token': csrfToken,
      }
    }
    return headers
  }

  /**
   * Add CSRF token to FormData
   */
  const addCSRFToFormData = (formData: FormData): FormData => {
    if (csrfToken) {
      formData.append('_csrf_token', csrfToken)
    }
    return formData
  }

  /**
   * Create a hidden CSRF input element
   */
  const createCSRFInput = (): React.ReactElement | null => {
    if (!csrfToken) return null

    return (
      <input
        key="csrf-token"
        type="hidden"
        name="_csrf_token"
        value={csrfToken}
      />
    )
  }

  return {
    csrfToken,
    addCSRFHeaders,
    addCSRFToFormData,
    createCSRFInput,
  }
}
