/**
 * BFF Configuration
 * Centralized configuration for BFF communication with proper type safety
 */

const BFF_BASE_URL = process.env.BFF_URL || 'http://localhost:4000'
const BFF_INTERNAL_KEY = process.env.BFF_INTERNAL_KEY

if (!BFF_INTERNAL_KEY) {
  throw new Error('BFF_INTERNAL_KEY environment variable is required')
}

// Type-safe exports
export const BFF_CONFIG = {
  baseUrl: BFF_BASE_URL,
  internalKey: BFF_INTERNAL_KEY,
} as const

/**
 * Creates headers for BFF requests with proper authentication
 */
export function createBFFHeaders(accessToken: string): Record<string, string> {
  return {
    'Content-Type': 'application/json',
    'X-Internal-Key': BFF_CONFIG.internalKey,
    Authorization: `Bearer ${accessToken}`,
  }
}

/**
 * Creates headers for BFF requests without content type (for GET requests)
 */
export function createBFFHeadersNoContent(
  accessToken: string
): Record<string, string> {
  return {
    'X-Internal-Key': BFF_CONFIG.internalKey,
    Authorization: `Bear<PERSON> ${accessToken}`,
  }
}
