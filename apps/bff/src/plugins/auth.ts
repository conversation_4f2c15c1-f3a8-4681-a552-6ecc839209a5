/* eslint-disable @typescript-eslint/no-misused-promises, @typescript-eslint/require-await */
import { FastifyPluginCallback, FastifyRequest } from 'fastify'
import fastifyPlugin from 'fastify-plugin'
import { createAuditLogger } from '../middleware/audit-logging'

interface JWTPayload {
  sub?: string
  user_id?: string
  email?: string
  exp?: number
  iat?: number
  aud?: string
  iss?: string
  role?: string
}

/**
 * Parse JWT token without verification (for inspection only)
 * This is used for additional validation checks, not for security
 */
function parseJWTPayload(token: string): JWTPayload | null {
  try {
    const parts = token.split('.')
    if (parts.length !== 3) return null

    const payload = parts[1]
    if (!payload) return null

    const decoded = Buffer.from(payload, 'base64').toString('utf8')
    return JSON.parse(decoded) as JWTPayload
  } catch {
    return null
  }
}

export interface AuthPluginOptions {
  internalKey: string
  enableRateLimiting?: boolean
}

declare module 'fastify' {
  interface FastifyRequest {
    // eslint-disable-line no-unused-vars
    isAuthenticated: boolean
    userToken?: string
    isUserRequest: boolean
    userId?: string
    userEmail?: string
  }
}

const authPlugin: FastifyPluginCallback<AuthPluginOptions> = async (
  fastify,
  options
) => {
  fastify.decorateRequest('isAuthenticated', false)
  fastify.decorateRequest('userToken', null)
  fastify.decorateRequest('isUserRequest', false)
  fastify.decorateRequest('userId', null)
  fastify.decorateRequest('userEmail', null)

  // Track failed authentication attempts for rate limiting
  const failedAttempts = new Map<
    string,
    { count: number; lastAttempt: number }
  >()
  const MAX_FAILED_ATTEMPTS = 5
  const LOCKOUT_DURATION = 15 * 60 * 1000 // 15 minutes
  const rateLimitingEnabled = options.enableRateLimiting !== false // Default to true

  const isRateLimited = (identifier: string): boolean => {
    const attempts = failedAttempts.get(identifier)
    if (!attempts) return false

    const now = Date.now()
    if (now - attempts.lastAttempt > LOCKOUT_DURATION) {
      failedAttempts.delete(identifier)
      return false
    }

    return attempts.count >= MAX_FAILED_ATTEMPTS
  }

  const recordFailedAttempt = (identifier: string): void => {
    const now = Date.now()
    const attempts = failedAttempts.get(identifier) || {
      count: 0,
      lastAttempt: now,
    }

    attempts.count += 1
    attempts.lastAttempt = now
    failedAttempts.set(identifier, attempts)
  }

  fastify.addHook('preHandler', async (request: FastifyRequest, reply) => {
    // Skip auth for health check
    if (request.url === '/healthz') {
      request.isAuthenticated = true
      return
    }

    const internalKey = request.headers['x-internal-key']
    const authHeader = request.headers.authorization
    const clientIP = request.ip || 'unknown'

    // Check rate limiting for failed authentication attempts
    if (rateLimitingEnabled && isRateLimited(clientIP)) {
      fastify.log.warn({ ip: clientIP }, 'Authentication rate limited')
      void reply.status(429).send({
        success: false,
        error:
          'Too many failed authentication attempts. Please try again later.',
      })
      return
    }

    // Check for internal service-to-service authentication
    if (internalKey && internalKey === options.internalKey) {
      request.isAuthenticated = true

      // Check if this is a user request (has Authorization header)
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7)

        // Validate JWT token comprehensively
        try {
          // Parse JWT payload for additional validation (without verification)
          const payload = parseJWTPayload(token)
          if (!payload) {
            fastify.log.warn('JWT validation failed - malformed token')
            void reply.status(401).send({
              success: false,
              error: 'Malformed token',
            })
            return
          }

          // Check token expiration
          if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
            fastify.log.warn(
              { exp: payload.exp },
              'JWT validation failed - token expired'
            )
            void reply.status(401).send({
              success: false,
              error: 'Token expired',
            })
            return
          }

          // Check token audience (should match Supabase project)
          if (payload.aud && !payload.aud.includes('authenticated')) {
            fastify.log.warn(
              { aud: payload.aud },
              'JWT validation failed - invalid audience'
            )
            void reply.status(401).send({
              success: false,
              error: 'Invalid token audience',
            })
            return
          }

          // Create user-scoped Supabase client for validation
          const userClient = fastify.createUserClient(token)

          // Validate token by attempting to get user (this verifies signature)
          const {
            data: { user },
            error: userError,
          } = await userClient.auth.getUser()

          if (userError || !user) {
            fastify.log.warn(
              { error: userError },
              'JWT validation failed - invalid or expired token'
            )
            void reply.status(401).send({
              success: false,
              error: 'Invalid or expired user token',
            })
            return
          }

          // Verify user ID matches token payload
          if (payload.sub && payload.sub !== user.id) {
            fastify.log.warn(
              { tokenSub: payload.sub, userId: user.id },
              'JWT validation failed - user ID mismatch'
            )
            void reply.status(401).send({
              success: false,
              error: 'Token user mismatch',
            })
            return
          }

          // Additional validation: user is active (basic check)
          // Note: Extended user banning logic would require custom user metadata or database lookup

          // Token is valid, set request properties
          request.userToken = token
          request.isUserRequest = true
          request.userSupabase = userClient
          request.userId = user.id
          request.userEmail = user.email || undefined

          // Log successful authentication for audit
          fastify.log.debug(
            {
              userId: user.id,
              email: user.email,
              ip: clientIP,
            },
            'User authenticated successfully'
          )

          // Audit log authentication success
          try {
            const auditLogger = createAuditLogger(request, userClient)
            await auditLogger.logAuthenticationSuccess('JWT')
          } catch (auditError) {
            fastify.log.error(
              { auditError },
              'Failed to log authentication success audit event'
            )
          }
        } catch (error) {
          // Record failed attempt for rate limiting
          if (rateLimitingEnabled) {
            recordFailedAttempt(clientIP)
          }

          fastify.log.error(
            {
              error,
              ip: clientIP,
              userAgent: request.headers['user-agent'],
            },
            'JWT validation error'
          )

          // Audit log authentication failure
          try {
            const auditLogger = createAuditLogger(request)
            await auditLogger.logAuthenticationFailed(
              'JWT',
              error instanceof Error ? error.message : 'Token validation failed'
            )
          } catch (auditError) {
            fastify.log.error(
              { auditError },
              'Failed to log authentication failure audit event'
            )
          }

          void reply.status(401).send({
            success: false,
            error: 'Token validation failed',
          })
          return
        }
      }

      return
    }

    // No valid authentication - record failed attempt
    if (rateLimitingEnabled) {
      recordFailedAttempt(clientIP)
    }
    request.isAuthenticated = false

    fastify.log.warn(
      {
        ip: clientIP,
        userAgent: request.headers['user-agent'],
        url: request.url,
      },
      'Authentication failed - missing or invalid credentials'
    )

    void reply.status(401).send({
      success: false,
      error: 'Unauthorized: Missing or invalid authentication',
    })
  })

  fastify.log.info('Auth plugin initialized with user context support')
}

export default fastifyPlugin(authPlugin, {
  name: 'auth-plugin',
})
