/**
 * Vitest Test Setup
 * Global test configuration and mocks
 */
import { beforeEach, afterEach, vi } from 'vitest'

// Mock environment variables for testing
process.env.SUPABASE_URL = 'http://localhost:54321'
process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-role-key'
process.env.BFF_INTERNAL_KEY = 'test-internal-key'
process.env.NODE_ENV = 'test'

// Global test timeout
vi.setConfig({ testTimeout: 10000 })

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
}

// Global beforeEach to reset mocks
beforeEach(() => {
  vi.clearAllMocks()
})

// Global afterEach cleanup
afterEach(() => {
  vi.restoreAllMocks()
})
