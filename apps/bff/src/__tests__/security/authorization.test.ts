/**
 * Security Tests for Authorization System
 * Tests entity-level access control and role-based permissions
 */

/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-argument */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-explicit-any */

import { FastifyInstance } from 'fastify'
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { createTestApp } from '../helpers/test-app'
import { createMockSupabaseClient } from '../helpers/mock-supabase'
import {
  validateEntityAccess,
  createEntityAuthMiddleware,
  EntityRole,
} from '../../middleware/entity-authorization'

describe('Authorization Security', () => {
  let app: FastifyInstance

  beforeEach(async () => {
    app = await createTestApp()
  })

  afterEach(async () => {
    await app.close()
  })

  describe('Entity Access Validation', () => {
    it('should deny access to non-existent entities', async () => {
      const mockSupabase = createMockSupabaseClient()
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: null,
              error: { message: 'Entity not found' },
            }),
          }),
        }),
      })

      const mockRequest = createMockRequest({
        params: { id: '999' },
        userSupabase: mockSupabase,
        isUserRequest: true,
        userId: 'user-123',
      })
      const mockReply = createMockReply()

      const result = await validateEntityAccess(mockRequest, mockReply)

      expect(result.success).toBe(false)
      expect(result.error).toContain('Entity not found or access denied')
      expect(mockReply.status).toHaveBeenCalledWith(404)
    })

    it('should deny access when user has insufficient permissions', async () => {
      const mockSupabase = createMockSupabaseClient()
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: { entity_id: 1, role: 'viewer' },
              error: null,
            }),
          }),
        }),
      })

      const mockRequest = createMockRequest({
        params: { id: '1' },
        userSupabase: mockSupabase,
        isUserRequest: true,
        userId: 'user-123',
      })
      const mockReply = createMockReply()

      const result = await validateEntityAccess(mockRequest, mockReply, {
        requiredRoles: ['owner', 'admin', 'accountant'],
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('Insufficient permissions')
      expect(mockReply.status).toHaveBeenCalledWith(403)
    })

    it('should grant access when user has sufficient permissions', async () => {
      const mockSupabase = createMockSupabaseClient()
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: { entity_id: 1, role: 'admin' },
              error: null,
            }),
          }),
        }),
      })

      const mockRequest = createMockRequest({
        params: { id: '1' },
        userSupabase: mockSupabase,
        isUserRequest: true,
        userId: 'user-123',
      })
      const mockReply = createMockReply()

      const result = await validateEntityAccess(mockRequest, mockReply, {
        requiredRoles: ['owner', 'admin', 'accountant'],
      })

      expect(result.success).toBe(true)
      expect(result.entityId).toBe(1)
      expect(result.userRole).toBe('admin')
    })

    it('should handle service role access when allowed', async () => {
      const mockRequest = createMockRequest({
        params: { id: '1' },
        isUserRequest: false, // Service role request
      })
      const mockReply = createMockReply()

      const result = await validateEntityAccess(mockRequest, mockReply, {
        allowServiceRole: true,
      })

      expect(result.success).toBe(true)
      expect(result.entityId).toBe(1)
      expect(result.userRole).toBe('owner') // Service role treated as owner
    })

    it('should reject service role access when not allowed', async () => {
      const mockRequest = createMockRequest({
        params: { id: '1' },
        isUserRequest: false, // Service role request
      })
      const mockReply = createMockReply()

      const result = await validateEntityAccess(mockRequest, mockReply, {
        allowServiceRole: false,
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('User authentication required')
      expect(mockReply.status).toHaveBeenCalledWith(401)
    })
  })

  describe('Role-Based Access Control', () => {
    const rolePermissionTests = [
      { role: 'owner', requiredRoles: ['owner'], shouldPass: true },
      { role: 'admin', requiredRoles: ['owner'], shouldPass: false },
      { role: 'admin', requiredRoles: ['owner', 'admin'], shouldPass: true },
      {
        role: 'accountant',
        requiredRoles: ['owner', 'admin', 'accountant'],
        shouldPass: true,
      },
      {
        role: 'bookkeeper',
        requiredRoles: ['owner', 'admin'],
        shouldPass: false,
      },
      {
        role: 'viewer',
        requiredRoles: ['owner', 'admin', 'accountant', 'bookkeeper'],
        shouldPass: false,
      },
    ]

    rolePermissionTests.forEach(({ role, requiredRoles, shouldPass }) => {
      it(`should ${shouldPass ? 'allow' : 'deny'} ${role} access to ${requiredRoles.join('/')} operations`, async () => {
        const mockSupabase = createMockSupabaseClient()
        mockSupabase.from.mockReturnValue({
          select: vi.fn().mockReturnValue({
            eq: vi.fn().mockReturnValue({
              single: vi.fn().mockResolvedValue({
                data: { entity_id: 1, role },
                error: null,
              }),
            }),
          }),
        })

        const mockRequest = createMockRequest({
          params: { id: '1' },
          userSupabase: mockSupabase,
          isUserRequest: true,
          userId: 'user-123',
        })
        const mockReply = createMockReply()

        const result = await validateEntityAccess(mockRequest, mockReply, {
          requiredRoles: requiredRoles as EntityRole[],
        })

        expect(result.success).toBe(shouldPass)
        if (shouldPass) {
          expect(result.userRole).toBe(role)
        } else {
          expect(result.error).toContain('Insufficient permissions')
        }
      })
    })
  })

  describe('Entity Authorization Middleware', () => {
    it('should add entity information to request when authorization succeeds', async () => {
      const mockSupabase = createMockSupabaseClient()
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: { entity_id: 1, role: 'admin' },
              error: null,
            }),
          }),
        }),
      })

      const mockRequest = createMockRequest({
        params: { id: '1' },
        userSupabase: mockSupabase,
        isUserRequest: true,
        userId: 'user-123',
      })
      const mockReply = createMockReply()

      const middleware = createEntityAuthMiddleware({
        requiredRoles: ['admin', 'owner'],
      })

      await middleware(mockRequest, mockReply)

      expect(mockRequest.entityId).toBe(1)
      expect(mockRequest.userRole).toBe('admin')
    })

    it('should not proceed when authorization fails', async () => {
      const mockSupabase = createMockSupabaseClient()
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: null,
              error: { message: 'Access denied' },
            }),
          }),
        }),
      })

      const mockRequest = createMockRequest({
        params: { id: '1' },
        userSupabase: mockSupabase,
        isUserRequest: true,
        userId: 'user-123',
      })
      const mockReply = createMockReply()

      const middleware = createEntityAuthMiddleware()

      await middleware(mockRequest, mockReply)

      expect(mockRequest.entityId).toBeUndefined()
      expect(mockRequest.userRole).toBeUndefined()
      expect(mockReply.status).toHaveBeenCalledWith(404)
    })
  })

  describe('Cross-Entity Access Prevention', () => {
    it('should prevent users from accessing entities they do not belong to', async () => {
      const mockSupabase = createMockSupabaseClient()
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: null,
              error: { message: 'No rows returned' },
            }),
          }),
        }),
      })

      const mockRequest = createMockRequest({
        params: { id: '999' }, // Entity user doesn't have access to
        userSupabase: mockSupabase,
        isUserRequest: true,
        userId: 'user-123',
      })
      const mockReply = createMockReply()

      const result = await validateEntityAccess(mockRequest, mockReply)

      expect(result.success).toBe(false)
      expect(result.error).toContain('Entity not found or access denied')
    })
  })

  describe('Parameter Validation', () => {
    it('should reject invalid entity ID formats', async () => {
      const mockRequest = createMockRequest({
        params: { id: 'invalid-id' },
        isUserRequest: true,
        userId: 'user-123',
      })
      const mockReply = createMockReply()

      const result = await validateEntityAccess(mockRequest, mockReply)

      expect(result.success).toBe(false)
      expect(result.error).toContain('Invalid entity ID format')
      expect(mockReply.status).toHaveBeenCalledWith(400)
    })

    it('should reject missing entity ID parameters', async () => {
      const mockRequest = createMockRequest({
        params: {}, // Missing ID parameter
        isUserRequest: true,
        userId: 'user-123',
      })
      const mockReply = createMockReply()

      const result = await validateEntityAccess(mockRequest, mockReply)

      expect(result.success).toBe(false)
      expect(result.error).toContain('Missing entity ID parameter')
      expect(mockReply.status).toHaveBeenCalledWith(400)
    })
  })
})

// Helper functions for creating mock objects
function createMockRequest(overrides: any = {}) {
  return {
    params: {},
    log: {
      info: vi.fn(),
      debug: vi.fn(),
      error: vi.fn(),
    },
    ...overrides,
  }
}

function createMockReply() {
  const reply = {
    status: vi.fn().mockReturnThis(),
    send: vi.fn().mockReturnThis(),
    code: vi.fn().mockReturnThis(),
    header: vi.fn().mockReturnThis(),
    headers: vi.fn().mockReturnThis(),
    type: vi.fn().mockReturnThis(),
    serializer: vi.fn().mockReturnThis(),
    raw: {},
    context: {},
    elapsedTime: 0,
    log: {
      info: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn(),
      trace: vi.fn(),
      fatal: vi.fn(),
      child: vi.fn(),
    },
    request: {},
    server: {},
    getHeader: vi.fn(),
    getHeaders: vi.fn(),
    hasHeader: vi.fn(),
    removeHeader: vi.fn(),
    redirect: vi.fn(),
    callNotFound: vi.fn(),
    getResponseTime: vi.fn(),
    then: vi.fn(),
    hijack: vi.fn(),
    sent: false,
    statusCode: 200,
  }
  return reply as any
}
