/**
 * Test Application Helper
 * Creates a configured Fastify instance for testing
 */

import { buildApp } from '../../index'
import type { FastifyInstance } from 'fastify'

export async function createTestApp(): Promise<FastifyInstance> {
  // Use the same buildApp function as production but with test config
  process.env.NODE_ENV = 'test'
  process.env.BFF_INTERNAL_KEY =
    process.env.BFF_INTERNAL_KEY || 'test-internal-key'
  process.env.SUPABASE_URL =
    process.env.SUPABASE_URL || 'http://localhost:54321'
  process.env.SUPABASE_SERVICE_ROLE_KEY =
    process.env.SUPABASE_SERVICE_ROLE_KEY || 'test-service-key'
  process.env.SUPABASE_ANON_KEY =
    process.env.SUPABASE_ANON_KEY || 'test-anon-key'
  process.env.INTERNAL_KEY = process.env.BFF_INTERNAL_KEY || 'test-internal-key'
  process.env.BFF_PORT = '0' // Use random port for tests

  const app = await buildApp()
  await app.ready()
  return app
}

export async function createAuthenticatedTestApp(): Promise<FastifyInstance> {
  const app = await createTestApp()

  // Add pre-handler to simulate authenticated requests
  // eslint-disable-next-line @typescript-eslint/require-await
  app.addHook('preHandler', async request => {
    if (request.headers.authorization?.includes('valid-token')) {
      request.isAuthenticated = true
      request.isUserRequest = true
      request.userId = 'test-user-123'
      request.userEmail = '<EMAIL>'
    }
  })

  return app
}
