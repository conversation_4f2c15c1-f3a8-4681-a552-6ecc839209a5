/**
 * Mock Supabase Client Helper
 * Creates mock Supabase clients for testing
 */
import { vi } from 'vitest'

export function createMockSupabaseClient() {
  return {
    auth: {
      getUser: vi.fn(),
      signInWithPassword: vi.fn(),
      signOut: vi.fn(),
    },
    from: vi.fn().mockReturnValue({
      select: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          single: vi.fn(),
          limit: vi.fn(),
        }),
        in: vi.fn().mockReturnValue({
          single: vi.fn(),
          limit: vi.fn(),
        }),
      }),
      insert: vi.fn().mockReturnValue({
        select: vi.fn(),
      }),
      update: vi.fn().mockReturnValue({
        eq: vi.fn().mockReturnValue({
          select: vi.fn(),
        }),
      }),
      delete: vi.fn().mockReturnValue({
        eq: vi.fn(),
      }),
    }),
    rpc: vi.fn(),
  }
}

export function createMockUserSupabaseClient(userId: string = 'test-user-123') {
  const client = createMockSupabaseClient()

  // Mock successful user authentication
  client.auth.getUser.mockResolvedValue({
    data: {
      user: {
        id: userId,
        email: '<EMAIL>',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    },
    error: null,
  })

  return client
}

export function createMockEntityAccess(
  entityId: number,
  role: string = 'admin'
) {
  return {
    data: {
      entity_id: entityId,
      role,
      user_id: 'test-user-123',
    },
    error: null,
  }
}
