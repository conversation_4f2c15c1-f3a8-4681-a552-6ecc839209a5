/**
 * Entity-Level Authorization Middleware
 * Provides reusable entity access validation for BFF routes
 */

import { FastifyRequest, FastifyReply } from 'fastify'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@belbooks/types'

export type EntityRole =
  | 'owner'
  | 'admin'
  | 'accountant'
  | 'bookkeeper'
  | 'viewer'

export interface EntityAuthOptions {
  requiredRoles?: EntityRole[]
  allowServiceRole?: boolean
  entityIdParam?: string
}

export interface EntityAuthResult {
  success: boolean
  entityId?: number
  userRole?: EntityRole
  error?: string
}

/**
 * Default entity authorization options
 */
const DEFAULT_OPTIONS: Required<EntityAuthOptions> = {
  requiredRoles: ['owner', 'admin', 'accountant', 'bookkeeper'],
  allowServiceRole: false,
  entityIdParam: 'id',
}

/**
 * Validate user access to an entity with specific role requirements
 */
export async function validateEntityAccess(
  request: FastifyRequest,
  reply: FastifyReply,
  options: EntityAuthOptions = {}
): Promise<EntityAuthResult> {
  const opts = { ...DEFAULT_OPTIONS, ...options }

  try {
    // Extract entity ID from request parameters
    const params = request.params as Record<string, string>
    const entityIdStr = params[opts.entityIdParam]

    if (!entityIdStr) {
      const error = `Missing entity ID parameter: ${opts.entityIdParam}`
      void reply.status(400).send({
        success: false,
        error,
      })
      return { success: false, error }
    }

    const entityId = parseInt(entityIdStr, 10)
    if (isNaN(entityId)) {
      const error = 'Invalid entity ID format'
      void reply.status(400).send({
        success: false,
        error,
      })
      return { success: false, error }
    }

    // Check if service role is allowed and being used
    if (opts.allowServiceRole && !request.isUserRequest) {
      // Service role has full access - log for audit
      request.log.info(
        {
          entityId,
          serviceRole: true,
        },
        'Service role entity access granted'
      )

      return {
        success: true,
        entityId,
        userRole: 'owner', // Service role treated as owner
      }
    }

    // Require user authentication
    const userClient = request.userSupabase
    if (!userClient || !request.isUserRequest || !request.userId) {
      const error = 'User authentication required'
      void reply.status(401).send({
        success: false,
        error,
      })
      return { success: false, error }
    }

    // Validate entity access through RLS-enforced view
    const { data: entityAccess, error: entityError } = await userClient
      .from('v_user_entities')
      .select('entity_id, role')
      .eq('entity_id', entityId)
      .single()

    if (entityError || !entityAccess) {
      const error = 'Entity not found or access denied'
      void reply.status(404).send({
        success: false,
        error,
      })
      return { success: false, error }
    }

    // Check role permissions
    const userRole = entityAccess.role as EntityRole
    if (!opts.requiredRoles.includes(userRole)) {
      const error = `Insufficient permissions. Required: ${opts.requiredRoles.join(', ')}, User has: ${userRole}`
      void reply.status(403).send({
        success: false,
        error,
      })
      return { success: false, error }
    }

    // Log successful authorization for audit
    request.log.debug(
      {
        userId: request.userId,
        entityId,
        userRole,
        requiredRoles: opts.requiredRoles,
      },
      'Entity access authorized'
    )

    return {
      success: true,
      entityId,
      userRole,
    }
  } catch (error) {
    request.log.error(error, 'Entity authorization error')
    const errorMessage = 'Authorization validation failed'
    void reply.status(500).send({
      success: false,
      error: errorMessage,
    })
    return { success: false, error: errorMessage }
  }
}

/**
 * Middleware factory for entity authorization
 * Returns a Fastify preHandler hook that validates entity access
 */
export function createEntityAuthMiddleware(options: EntityAuthOptions = {}) {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    const result = await validateEntityAccess(request, reply, options)

    if (!result.success) {
      // Response already sent by validateEntityAccess
      return
    }

    // Add entity info to request for downstream handlers
    request.entityId = result.entityId
    request.userRole = result.userRole
  }
}

/**
 * Utility function to get authenticated user client with entity validation
 */
export async function getAuthenticatedUserClientWithEntity(
  request: FastifyRequest,
  reply: FastifyReply,
  options: EntityAuthOptions = {}
): Promise<{
  client: SupabaseClient<Database> | null
  entityId: number | null
  userRole: EntityRole | null
}> {
  const authResult = await validateEntityAccess(request, reply, options)

  if (!authResult.success) {
    return { client: null, entityId: null, userRole: null }
  }

  return {
    client: request.userSupabase || null,
    entityId: authResult.entityId || null,
    userRole: authResult.userRole || null,
  }
}

// Extend FastifyRequest interface to include entity info
declare module 'fastify' {
  interface FastifyRequest {
    entityId?: number
    userRole?: EntityRole
  }
}
