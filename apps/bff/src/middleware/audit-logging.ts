/**
 * Comprehensive Audit Logging System
 * Provides detailed security event logging for compliance and monitoring
 */

import { FastifyRequest } from 'fastify'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@belbooks/types'

export type AuditEventType =
  | 'BANK_IMPORT_STARTED'
  | 'BANK_IMPORT_COMPLETED'
  | 'BANK_IMPORT_FAILED'
  | 'BANK_TRANSACTION_ACCESSED'
  | 'BANK_TRANSACTION_MODIFIED'
  | 'ENTITY_ACCESS_GRANTED'
  | 'ENTITY_ACCESS_DENIED'
  | 'AUTHENTICATION_SUCCESS'
  | 'AUTHENTICATION_FAILED'
  | 'AUTHORIZATION_FAILED'
  | 'SUSPICIOUS_ACTIVITY'
  | 'DATA_EXPORT'
  | 'CONFIGURATION_CHANGED'

export type AuditSeverity = 'low' | 'medium' | 'high' | 'critical'

export interface AuditEventData {
  eventType: AuditEventType
  severity: AuditSeverity
  userId?: string
  entityId?: number
  resourceId?: string
  resourceType?: string
  action?: string
  details?: Record<string, unknown>
  ipAddress?: string
  userAgent?: string
  sessionId?: string
  timestamp?: string
  success?: boolean
  errorMessage?: string
}

export interface AuditContext {
  request: FastifyRequest
  supabaseClient?: SupabaseClient<Database>
  userId?: string
  entityId?: number
}

/**
 * Extract audit context from request
 */
function extractAuditContext(request: FastifyRequest): {
  ipAddress: string
  userAgent: string
  userId?: string
  sessionId?: string
} {
  return {
    ipAddress: request.ip || 'unknown',
    userAgent: request.headers['user-agent'] || 'unknown',
    userId: request.userId,
    sessionId: request.userToken
      ? request.userToken.substring(0, 8) + '...'
      : undefined,
  }
}

/**
 * Log audit event to database and application logs
 */
export async function logAuditEvent(
  context: AuditContext,
  eventData: Omit<AuditEventData, 'ipAddress' | 'userAgent' | 'timestamp'>
): Promise<void> {
  const { request, supabaseClient } = context
  const auditContext = extractAuditContext(request)

  const fullEventData: AuditEventData = {
    ...eventData,
    ...auditContext,
    timestamp: new Date().toISOString(),
    userId: eventData.userId || auditContext.userId,
    entityId: eventData.entityId || context.entityId,
  }

  // Log to application logs (always)
  const logLevel = getLogLevel(eventData.severity)
  // eslint-disable-next-line security/detect-object-injection
  request.log[logLevel](fullEventData, `Audit: ${eventData.eventType}`)

  // Log to database (if client available and not a database operation itself)
  if (supabaseClient && !isInternalDatabaseEvent(eventData.eventType)) {
    try {
      await supabaseClient.from('security_events').insert({
        event_type: eventData.eventType,
        severity: eventData.severity,
        user_id: fullEventData.userId || null,
        entity_id: fullEventData.entityId || null,
        resource_id: eventData.resourceId || null,
        resource_type: eventData.resourceType || null,
        action: eventData.action || null,
        details: eventData.details ? JSON.stringify(eventData.details) : null,
        ip_address: auditContext.ipAddress,
        user_agent: auditContext.userAgent,
        session_id: auditContext.sessionId || null,
        success: eventData.success ?? true,
        error_message: eventData.errorMessage || null,
        created_at: fullEventData.timestamp,
      })
    } catch (error) {
      // Don't fail the main operation if audit logging fails
      request.log.error(
        { error, eventData: fullEventData },
        'Failed to log audit event to database'
      )
    }
  }
}

/**
 * Get appropriate log level for severity
 */
function getLogLevel(
  severity: AuditSeverity
): 'debug' | 'info' | 'warn' | 'error' {
  switch (severity) {
    case 'low':
      return 'debug'
    case 'medium':
      return 'info'
    case 'high':
      return 'warn'
    case 'critical':
      return 'error'
    default:
      return 'info'
  }
}

/**
 * Check if event type is internal database operation to avoid recursion
 */
function isInternalDatabaseEvent(_eventType: AuditEventType): boolean {
  return false // Currently no internal database events defined
}

/**
 * Specialized audit logging functions for common scenarios
 */
export class AuditLogger {
  // eslint-disable-next-line no-unused-vars
  constructor(private context: AuditContext) {}

  async logBankImportStarted(
    bankAccountId: number,
    fileName?: string,
    format?: string
  ): Promise<void> {
    await logAuditEvent(this.context, {
      eventType: 'BANK_IMPORT_STARTED',
      severity: 'medium',
      resourceId: bankAccountId.toString(),
      resourceType: 'bank_account',
      action: 'import_start',
      details: {
        fileName,
        format,
        bankAccountId,
      },
      success: true,
    })
  }

  async logBankImportCompleted(
    bankAccountId: number,
    batchId: string,
    stats: { imported: number; skipped: number; deduped: number }
  ): Promise<void> {
    await logAuditEvent(this.context, {
      eventType: 'BANK_IMPORT_COMPLETED',
      severity: 'medium',
      resourceId: batchId,
      resourceType: 'import_batch',
      action: 'import_complete',
      details: {
        bankAccountId,
        batchId,
        ...stats,
        totalProcessed: stats.imported + stats.skipped + stats.deduped,
      },
      success: true,
    })
  }

  async logBankImportFailed(
    bankAccountId: number,
    error: string,
    details?: Record<string, unknown>
  ): Promise<void> {
    await logAuditEvent(this.context, {
      eventType: 'BANK_IMPORT_FAILED',
      severity: 'high',
      resourceId: bankAccountId.toString(),
      resourceType: 'bank_account',
      action: 'import_failed',
      details: {
        bankAccountId,
        ...details,
      },
      success: false,
      errorMessage: error,
    })
  }

  async logEntityAccessGranted(
    entityId: number,
    role: string,
    action: string
  ): Promise<void> {
    await logAuditEvent(this.context, {
      eventType: 'ENTITY_ACCESS_GRANTED',
      severity: 'low',
      entityId,
      resourceId: entityId.toString(),
      resourceType: 'entity',
      action,
      details: {
        role,
        grantedPermissions: action,
      },
      success: true,
    })
  }

  async logEntityAccessDenied(
    entityId: number,
    attemptedAction: string,
    reason: string
  ): Promise<void> {
    await logAuditEvent(this.context, {
      eventType: 'ENTITY_ACCESS_DENIED',
      severity: 'high',
      entityId,
      resourceId: entityId.toString(),
      resourceType: 'entity',
      action: attemptedAction,
      details: {
        reason,
        attemptedAction,
      },
      success: false,
      errorMessage: reason,
    })
  }

  async logAuthenticationSuccess(method: string): Promise<void> {
    await logAuditEvent(this.context, {
      eventType: 'AUTHENTICATION_SUCCESS',
      severity: 'low',
      action: 'authenticate',
      details: {
        method,
        authenticationMethod: method,
      },
      success: true,
    })
  }

  async logAuthenticationFailed(method: string, reason: string): Promise<void> {
    await logAuditEvent(this.context, {
      eventType: 'AUTHENTICATION_FAILED',
      severity: 'high',
      action: 'authenticate',
      details: {
        method,
        authenticationMethod: method,
        failureReason: reason,
      },
      success: false,
      errorMessage: reason,
    })
  }

  async logSuspiciousActivity(
    activity: string,
    details: Record<string, unknown>
  ): Promise<void> {
    await logAuditEvent(this.context, {
      eventType: 'SUSPICIOUS_ACTIVITY',
      severity: 'critical',
      action: 'suspicious_activity',
      details: {
        activity,
        ...details,
      },
      success: false,
      errorMessage: activity,
    })
  }
}

/**
 * Create audit logger for request context
 */
export function createAuditLogger(
  request: FastifyRequest,
  supabaseClient?: SupabaseClient<Database>,
  entityId?: number
): AuditLogger {
  return new AuditLogger({
    request,
    supabaseClient,
    userId: request.userId,
    entityId,
  })
}
