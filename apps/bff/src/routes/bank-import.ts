/* eslint-disable @typescript-eslint/no-misused-promises, @typescript-eslint/require-await */
import { FastifyPluginCallback } from 'fastify'
import {
  BankFileImportSchema,
  CsvImportConfigSchema,
  ApiResponse,
  type CsvImportConfig,
} from '@belbooks/types'
import { importService } from '@belbooks/import-service'
import { z } from 'zod'
import '../types'
import type { SupabaseClient } from '@supabase/supabase-js'
import type { Database } from '@belbooks/types'
import { getAuthenticatedUserClientWithEntity } from '../middleware/entity-authorization'
import { createAuditLogger } from '../middleware/audit-logging'

// Multipart form schema for file uploads
const MultipartImportSchema = z.object({
  entity_id: z.coerce.number(),
  bank_account_id: z.coerce.number(),
  format: z.enum(['coda', 'csv']).default('coda'),
  csv_config: z.string().optional(),
})

// Query parameter schemas
const EntityParamsSchema = z.object({
  id: z.string().transform(val => parseInt(val, 10)),
})

const bankImportRoutes: FastifyPluginCallback = async (fastify, _opts) => {
  // Import bank transactions from file (supports both JSON with base64 and multipart)
  fastify.post(
    '/entities/:id/bank-transactions/import',
    async (request, reply) => {
      try {
        const { id: entityId } = EntityParamsSchema.parse(request.params)

        let importData: {
          entity_id: number
          bank_account_id: number
          format: 'coda' | 'csv'
          file_content?: string
          filename?: string
          csv_config?: CsvImportConfig
        }
        let fileContent: string
        let filename: string | undefined

        // Handle multipart file upload
        if (request.isMultipart()) {
          const parts = request.parts()
          const formData: Record<string, unknown> = {}
          let fileBuffer: Buffer | undefined

          for await (const part of parts) {
            if (part.type === 'file') {
              fileBuffer = await part.toBuffer()
              filename = part.filename
            } else {
              formData[part.fieldname] = part.value
            }
          }

          if (!fileBuffer) {
            const response: ApiResponse = {
              success: false,
              error: 'No file provided',
            }
            return reply.code(400).send(response)
          }

          const parsedForm = MultipartImportSchema.parse(formData)
          let csvConfig: CsvImportConfig | undefined
          if (
            parsedForm.format === 'csv' &&
            typeof formData.csv_config === 'string'
          ) {
            try {
              const parsedConfig = JSON.parse(formData.csv_config) as unknown
              csvConfig = CsvImportConfigSchema.parse(parsedConfig)
            } catch (error) {
              const response: ApiResponse = {
                success: false,
                error: `Invalid csv_config payload: ${
                  error instanceof Error ? error.message : 'unknown error'
                }`,
              }
              return reply.code(400).send(response)
            }
          }

          importData = {
            entity_id: parsedForm.entity_id,
            bank_account_id: parsedForm.bank_account_id,
            format: parsedForm.format,
            ...(csvConfig ? { csv_config: csvConfig } : {}),
          }
          fileContent = fileBuffer.toString('utf-8')
        } else {
          // Handle JSON with base64 content
          const jsonData = BankFileImportSchema.parse(request.body)
          importData = {
            entity_id: jsonData.entity_id,
            bank_account_id: jsonData.bank_account_id,
            format: jsonData.format,
            ...(jsonData.csv_config ? { csv_config: jsonData.csv_config } : {}),
          }
          filename = jsonData.filename

          // Decode base64 file content
          try {
            fileContent = Buffer.from(jsonData.file_content, 'base64').toString(
              'utf-8'
            )
          } catch (error) {
            const response: ApiResponse = {
              success: false,
              error: 'Invalid base64 file content',
            }
            return reply.code(400).send(response)
          }
        }

        // Validate that entity_id matches the route parameter
        if (importData.entity_id !== entityId) {
          const response: ApiResponse = {
            success: false,
            error: 'Entity ID in request body must match route parameter',
          }
          return reply.code(400).send(response)
        }

        // Validate user authentication and entity access
        const { client: userClient, entityId: validatedEntityId } =
          await getAuthenticatedUserClientWithEntity(request, reply, {
            requiredRoles: ['owner', 'admin', 'accountant', 'bookkeeper'],
            entityIdParam: 'id',
          })

        if (!userClient || !validatedEntityId) {
          // Response already sent by middleware
          return
        }

        // Create audit logger for this operation
        const auditLogger = createAuditLogger(
          request,
          userClient,
          validatedEntityId
        )

        // Verify the bank account exists and belongs to the entity (RLS enforced)
        const { data: bankAccount, error: bankAccountError } = await userClient
          .from('bank_accounts')
          .select('id, entity_id')
          .eq('id', importData.bank_account_id)
          .eq('entity_id', entityId)
          .single()

        if (bankAccountError || !bankAccount) {
          await auditLogger.logBankImportFailed(
            importData.bank_account_id,
            'Bank account not found or access denied',
            {
              entityId: validatedEntityId,
              bankAccountId: importData.bank_account_id,
            }
          )
          const response: ApiResponse = {
            success: false,
            error: 'Bank account not found or does not belong to this entity',
          }
          return reply.code(404).send(response)
        }

        // Log successful bank import start
        await auditLogger.logBankImportStarted(
          importData.bank_account_id,
          importData.filename,
          importData.format
        )

        if (importData.format === 'coda' && !fileContent.startsWith('0000')) {
          const response: ApiResponse = {
            success: false,
            error: 'CODA files must start with record 0000',
          }
          return reply.code(400).send(response)
        }

        // Parse file using import service
        const importResult = importService.importFile(
          fileContent,
          importData.format,
          filename,
          importData.csv_config
        )

        // Convert normalized transactions for database storage
        const dbTransactions = importResult.entries.map(tx => ({
          transaction_date: tx.transaction_date,
          value_date: tx.value_date,
          amount: tx.amount,
          description: tx.description,
          transaction_id: tx.transaction_id,
          counterparty_name: tx.counterparty_name,
          reference: tx.reference,
          // Enhanced fields from CODA v2 parser
          dedupe_hash: tx.dedupe_hash,
          currency: tx.currency,
          structured_ref: tx.structured_ref,
          raw_json: tx.raw_json,
        })) as unknown

        // Call the database RPC function to import transactions with user context
        // This will use the updated RPC that handles both user and service role contexts
        const { data: rpcResult, error: importError } = await userClient.rpc(
          'rpc_import_bank_transactions',
          {
            p_entity: entityId,
            p_bank_account_id: importData.bank_account_id,
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-explicit-any
            p_transactions: dbTransactions as any,
            p_batch_id: importResult.batchId,
          }
        )

        if (importError) {
          throw new Error(
            `Failed to import transactions: ${importError.message}`
          )
        }

        // Build result response using RPC result if available
        const rpcData = rpcResult as Record<string, unknown> | null
        const result = {
          imported:
            (rpcData?.imported as number) || importResult.summary.imported,
          skipped: (rpcData?.skipped as number) || importResult.summary.skipped,
          deduped: (rpcData?.deduped as number) || 0,
          warnings: importResult.warnings,
          errors: (rpcData?.errors as string[]) || [],
          batch_id: (rpcData?.batch_id as string) || importResult.batchId,
          transactions: importResult.entries.map((tx, index) => ({
            id: index + 1,
            status: 'imported',
            amount: tx.amount.toString(),
            description: tx.description,
          })),
        }

        try {
          if (result.batch_id) {
            await generateMatchSuggestions(
              userClient,
              entityId,
              importData.bank_account_id,
              result.batch_id
            )
          }
        } catch (suggestionError) {
          fastify.log.error(
            { suggestionError },
            'Failed to generate match suggestions'
          )
        }

        // Log successful completion
        await auditLogger.logBankImportCompleted(
          importData.bank_account_id,
          result.batch_id,
          {
            imported: result.imported,
            skipped: result.skipped,
            deduped: result.deduped,
          }
        )

        const response: ApiResponse = {
          success: true,
          data: result,
        }

        return reply.code(201).send(response)
      } catch (error) {
        fastify.log.error(error)

        // Log the failure with audit logger if available
        try {
          const auditLogger = createAuditLogger(request, request.userSupabase)
          await auditLogger.logBankImportFailed(
            (request.body as { bank_account_id?: number })?.bank_account_id ||
              0,
            error instanceof Error ? error.message : 'Unknown error occurred',
            {
              errorType:
                error instanceof Error
                  ? error.constructor.name
                  : 'UnknownError',
              stack: error instanceof Error ? error.stack : undefined,
            }
          )
        } catch (auditError) {
          fastify.log.error(
            { auditError },
            'Failed to log audit event for bank import failure'
          )
        }

        const response: ApiResponse = {
          success: false,
          error:
            error instanceof Error ? error.message : 'Unknown error occurred',
        }
        return reply.code(500).send(response)
      }
    }
  )
}

async function generateMatchSuggestions(
  supabaseClient: SupabaseClient<Database>,
  entityId: number,
  bankAccountId: number,
  batchId: string
): Promise<void> {
  const { data: transactions, error } = await supabaseClient
    .from('bank_transactions')
    .select('id, amount, transaction_date, counterparty_name, structured_ref')
    .eq('batch_id', batchId)
    .eq('status', 'unmatched')

  if (error) {
    throw new Error(
      `Failed to load transactions for suggestions: ${error.message}`
    )
  }

  if (!transactions || transactions.length === 0) {
    return
  }

  const transactionIds = transactions.map(tx => tx.id)
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const suggestionClient = supabaseClient as SupabaseClient<any>

  if (transactionIds.length > 0) {
    await suggestionClient
      .from('bank_match_suggestions')
      .delete()
      .in('bank_transaction_id', transactionIds)
      .eq('status', 'suggested')
  }

  for (const transaction of transactions) {
    const absAmount = Math.abs(Number(transaction.amount))
    const { data: invoices, error: invoiceError } = await supabaseClient
      .from('invoices')
      .select(
        'id, total_amount, number, counterparty_name, invoice_date, due_date, status, kind'
      )
      .eq('entity_id', entityId)
      .in('status', ['sent', 'overdue'])
      .eq('total_amount', absAmount)
      .limit(10)

    if (invoiceError || !invoices || invoices.length === 0) {
      continue
    }

    for (const invoice of invoices) {
      const structuredRefMatch =
        Boolean(transaction.structured_ref) &&
        sanitizeDigits(transaction.structured_ref) ===
          sanitizeDigits(invoice.number)

      const counterpartyMatch =
        Boolean(transaction.counterparty_name && invoice.counterparty_name) &&
        normalizeName(transaction.counterparty_name) ===
          normalizeName(invoice.counterparty_name)

      const dateDiff = invoice.invoice_date
        ? Math.abs(
            differenceInDays(
              new Date(transaction.transaction_date),
              new Date(invoice.invoice_date)
            )
          )
        : undefined

      let confidence = 0.6
      if (structuredRefMatch) confidence += 0.25
      if (counterpartyMatch) confidence += 0.1
      if (typeof dateDiff === 'number' && dateDiff <= 3) confidence += 0.05
      if (typeof dateDiff === 'number' && dateDiff <= 0) confidence += 0.02
      confidence = Math.min(confidence, 0.99)

      const reason = {
        amountMatch: true,
        structuredRefMatch,
        counterpartyMatch,
        dateDiffDays: dateDiff ?? null,
        transactionDate: transaction.transaction_date,
        invoiceDate: invoice.invoice_date,
        bankAccountId,
      }

      await suggestionClient.from('bank_match_suggestions').insert({
        entity_id: entityId,
        bank_transaction_id: transaction.id,
        invoice_id: invoice.id,
        confidence,
        recommended_amount: absAmount,
        reason,
      })
    }
  }
}

function sanitizeDigits(value?: string | null): string | null {
  if (!value) return null
  const digits = value.replace(/[^0-9]/g, '')
  return digits.length > 0 ? digits : null
}

function normalizeName(value?: string | null): string | null {
  if (!value) return null
  return value.trim().toLowerCase().replace(/\s+/g, ' ')
}

function differenceInDays(dateA: Date, dateB: Date): number {
  const msPerDay = 1000 * 60 * 60 * 24
  const diff = dateA.getTime() - dateB.getTime()
  return Math.round(diff / msPerDay)
}

export default bankImportRoutes
