/* eslint-disable @typescript-eslint/no-misused-promises, @typescript-eslint/require-await */
import { FastifyPluginCallback } from 'fastify'
import { ApiResponse, VATError, VATSetupRequestSchema } from '@belbooks/types'
import {
  rpcVatPreview,
  rpcVatExportCsv,
  checkVATEnabled,
  createVATDisabledError,
  saveVATConfiguration,
  getVATConfiguration
} from '@belbooks/dal'
import { z } from 'zod'
import '../types' // Import type extensions

// Query parameter schemas
const EntityParamsSchema = z.object({
  id: z.string().transform(val => parseInt(val, 10))
})

const VATQuerySchema = z.object({
  start: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format'),
  end: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format')
})

const vatRoutes: FastifyPluginCallback = async (fastify, _opts) => {
  // VAT Preview endpoint - returns JSON data for the VAT preview
  fastify.get('/entities/:id/vat/preview', async (request, reply) => {
    try {
      const { id: entityId } = EntityParamsSchema.parse(request.params)
      const { start, end } = VATQuerySchema.parse(request.query)

      // Use user-scoped client if available for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      // Check VAT feature flag using typed DAL function
      const isVATEnabled = await checkVATEnabled(supabaseClient, entityId)
      if (!isVATEnabled) {
        const vatError = createVATDisabledError()
        const response: ApiResponse = {
          success: false,
          error: vatError.message
        }
        return reply.code(403).send(response)
      }

      // Call the typed VAT preview function
      const vatPreviewData = await rpcVatPreview(supabaseClient, {
        p_entity: entityId,
        p_start: start,
        p_end: end
      })

      const response: ApiResponse = {
        success: true,
        data: vatPreviewData
      }

      return reply.send(response)
    } catch (error) {
      fastify.log.error(error)

      // Handle VAT-specific errors
      if (error instanceof Error && 'code' in error) {
        const vatError = error as VATError
        let statusCode = 500

        if (vatError.code === 'INVALID_DATE_FORMAT') {
          statusCode = 400
        } else if (vatError.code === 'VAT_DISABLED') {
          statusCode = 403
        }

        const response: ApiResponse = {
          success: false,
          error: vatError.message
        }
        return reply.code(statusCode).send(response)
      }

      const response: ApiResponse = {
        success: false,
        error:
          error instanceof Error ? error.message : 'Unknown error occurred'
      }
      return reply.code(500).send(response)
    }
  })

  // VAT CSV Export endpoint - returns CSV file for download
  fastify.get('/entities/:id/vat/export.csv', async (request, reply) => {
    try {
      const { id: entityId } = EntityParamsSchema.parse(request.params)
      const { start, end } = VATQuerySchema.parse(request.query)

      // Use user-scoped client if available for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      // Check VAT feature flag using typed DAL function
      const isVATEnabled = await checkVATEnabled(supabaseClient, entityId)
      if (!isVATEnabled) {
        const vatError = createVATDisabledError()
        void reply.code(403)
        void reply.header('Content-Type', 'text/plain')
        return reply.send(`Error: ${vatError.message}`)
      }

      // Call the typed VAT CSV export function
      const csvData = await rpcVatExportCsv(supabaseClient, {
        p_entity: entityId,
        p_start: start,
        p_end: end
      })

      // Set appropriate headers for CSV download
      const filename = `vat-export-${entityId}-${start}-to-${end}.csv`
      void reply.header('Content-Type', 'text/csv; charset=utf-8')
      void reply.header(
        'Content-Disposition',
        `attachment; filename="${filename}"`
      )

      return reply.send(csvData)
    } catch (error) {
      fastify.log.error(error)

      // For CSV endpoint, return error as text since we're expecting CSV format
      if (error instanceof Error && 'code' in error) {
        const vatError = error as VATError
        let statusCode = 500

        if (vatError.code === 'INVALID_DATE_FORMAT') {
          statusCode = 400
        } else if (vatError.code === 'VAT_DISABLED') {
          statusCode = 403
        }

        void reply.code(statusCode)
        void reply.header('Content-Type', 'text/plain')
        return reply.send(`Error: ${vatError.message}`)
      }

      void reply.code(500)
      void reply.header('Content-Type', 'text/plain')
      return reply.send(
        `Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`
      )
    }
  })

  // VAT Setup endpoint - configures VAT for an entity
  fastify.post('/entities/:id/vat/setup', async (request, reply) => {
    try {
      const { id: entityId } = EntityParamsSchema.parse(request.params)
      const vatSetupRequest = VATSetupRequestSchema.parse(request.body)

      // Use user-scoped client if available for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      // Save VAT configuration using typed DAL function
      const result = await saveVATConfiguration(
        supabaseClient,
        entityId,
        vatSetupRequest
      )

      if (!result.success) {
        const response: ApiResponse = {
          success: false,
          error: result.error || 'Failed to save VAT configuration'
        }
        return reply.code(400).send(response)
      }

      const response: ApiResponse = {
        success: true,
        data: result
      }

      return reply.send(response)
    } catch (error) {
      fastify.log.error(error)

      // Handle validation errors
      if (error instanceof z.ZodError) {
        const response: ApiResponse = {
          success: false,
          error: `Validation error: ${error.errors.map(e => e.message).join(', ')}`
        }
        return reply.code(400).send(response)
      }

      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error'
      }
      return reply.code(500).send(response)
    }
  })

  // VAT Configuration retrieval endpoint
  fastify.get('/entities/:id/vat/configuration', async (request, reply) => {
    try {
      const { id: entityId } = EntityParamsSchema.parse(request.params)

      // Use user-scoped client if available for RLS enforcement
      const supabaseClient = request.userSupabase || fastify.supabase

      // Get current VAT configuration
      const configuration = await getVATConfiguration(supabaseClient, entityId)

      const response: ApiResponse = {
        success: true,
        data: { configuration }
      }

      return reply.send(response)
    } catch (error) {
      fastify.log.error(error)

      const response: ApiResponse = {
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error'
      }
      return reply.code(500).send(response)
    }
  })
}

export default vatRoutes
