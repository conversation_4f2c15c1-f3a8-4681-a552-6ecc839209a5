-- pgTAP tests for Expense Classification System migration
-- Tests table structure, RLS policies, constraints, and business rules

BEGIN;

SELECT plan(42);

-- Load required extensions
SELECT has_extension('pgtap');

-- =============================================================================
-- TABLE STRUCTURE TESTS
-- =============================================================================

-- Test that all tables exist
SELECT has_table('expense_categories', 'expense_categories table should exist');
SELECT has_table('expense_category_rules', 'expense_category_rules table should exist');
SELECT has_table('posting_classifications', 'posting_classifications table should exist');
SELECT has_table('fiscal_params_be', 'fiscal_params_be table should exist');
SELECT has_table('tax_estimates', 'tax_estimates table should exist');

-- Test expense_categories structure
SELECT has_column('expense_categories', 'code', 'expense_categories should have code column');
SELECT has_column('expense_categories', 'name', 'expense_categories should have name column');
SELECT has_column('expense_categories', 'default_income_deduct_pct', 'expense_categories should have default_income_deduct_pct column');
SELECT has_column('expense_categories', 'default_vat_deduct_pct', 'expense_categories should have default_vat_deduct_pct column');
SELECT has_column('expense_categories', 'guidance_md', 'expense_categories should have guidance_md column');

-- Test expense_category_rules structure
SELECT has_column('expense_category_rules', 'entity_id', 'expense_category_rules should have entity_id column');
SELECT has_column('expense_category_rules', 'code', 'expense_category_rules should have code column');
SELECT has_column('expense_category_rules', 'income_deduct_pct', 'expense_category_rules should have income_deduct_pct column');
SELECT has_column('expense_category_rules', 'vat_deduct_pct', 'expense_category_rules should have vat_deduct_pct column');

-- Test posting_classifications structure
SELECT has_column('posting_classifications', 'entity_id', 'posting_classifications should have entity_id column');
SELECT has_column('posting_classifications', 'source_kind', 'posting_classifications should have source_kind column');
SELECT has_column('posting_classifications', 'source_id', 'posting_classifications should have source_id column');
SELECT has_column('posting_classifications', 'category_code', 'posting_classifications should have category_code column');
SELECT has_column('posting_classifications', 'confidence', 'posting_classifications should have confidence column');
SELECT has_column('posting_classifications', 'decided_by', 'posting_classifications should have decided_by column');

-- Test fiscal_params_be structure
SELECT has_column('fiscal_params_be', 'year', 'fiscal_params_be should have year column');
SELECT has_column('fiscal_params_be', 'payload_json', 'fiscal_params_be should have payload_json column');

-- Test tax_estimates structure
SELECT has_column('tax_estimates', 'entity_id', 'tax_estimates should have entity_id column');
SELECT has_column('tax_estimates', 'year', 'tax_estimates should have year column');
SELECT has_column('tax_estimates', 'payload_json', 'tax_estimates should have payload_json column');

-- =============================================================================
-- CONSTRAINT TESTS
-- =============================================================================

-- Test primary keys
SELECT has_pk('expense_categories', 'expense_categories should have primary key');
SELECT has_pk('expense_category_rules', 'expense_category_rules should have primary key');
SELECT has_pk('posting_classifications', 'posting_classifications should have primary key');
SELECT has_pk('fiscal_params_be', 'fiscal_params_be should have primary key');
SELECT has_pk('tax_estimates', 'tax_estimates should have primary key');

-- Test foreign key constraints
SELECT has_fk('expense_category_rules', 'expense_category_rules should have foreign key to entities');
SELECT has_fk('posting_classifications', 'posting_classifications should have foreign key to entities');
SELECT has_fk('tax_estimates', 'tax_estimates should have foreign key to entities');

-- Test check constraints for percentage fields
SELECT throws_ok(
  $$INSERT INTO expense_categories (code, name, default_income_deduct_pct) VALUES ('TEST', 'Test', 150)$$,
  23514,
  NULL,
  'default_income_deduct_pct > 100 should be rejected'
);

SELECT throws_ok(
  $$INSERT INTO expense_categories (code, name, default_vat_deduct_pct) VALUES ('TEST2', 'Test', -10)$$,
  23514,
  NULL,
  'default_vat_deduct_pct < 0 should be rejected'
);

-- Test check constraints for source_kind
SELECT throws_ok(
  $$INSERT INTO posting_classifications (entity_id, source_kind, source_id, category_code, decided_by) VALUES (1, 'invalid', 1, 'TEST', 'user')$$,
  23514,
  NULL,
  'Invalid source_kind should be rejected'
);

-- Test check constraints for decided_by
SELECT throws_ok(
  $$INSERT INTO posting_classifications (entity_id, source_kind, source_id, category_code, decided_by) VALUES (1, 'document', 1, 'TEST', 'invalid')$$,
  23514,
  NULL,
  'Invalid decided_by should be rejected'
);

-- =============================================================================
-- RLS POLICY TESTS
-- =============================================================================

-- Test that RLS is enabled on all tables (check pg_tables instead of row_security_active for superuser)
SELECT is((SELECT rowsecurity FROM pg_tables WHERE tablename = 'expense_categories'), true, 'RLS should be enabled on expense_categories');
SELECT is((SELECT rowsecurity FROM pg_tables WHERE tablename = 'expense_category_rules'), true, 'RLS should be enabled on expense_category_rules');
SELECT is((SELECT rowsecurity FROM pg_tables WHERE tablename = 'posting_classifications'), true, 'RLS should be enabled on posting_classifications');
SELECT is((SELECT rowsecurity FROM pg_tables WHERE tablename = 'fiscal_params_be'), true, 'RLS should be enabled on fiscal_params_be');
SELECT is((SELECT rowsecurity FROM pg_tables WHERE tablename = 'tax_estimates'), true, 'RLS should be enabled on tax_estimates');

-- Create test users for RLS testing
INSERT INTO auth.users (id, email, email_confirmed_at, created_at, updated_at)
VALUES
('11111111-1111-1111-1111-111111111111'::uuid, '<EMAIL>', now(), now(), now()),
('*************-2222-2222-************'::uuid, '<EMAIL>', now(), now(), now())
ON CONFLICT (id) DO NOTHING;

-- Create test tenants and entities with unique IDs
INSERT INTO tenants (id, name, org_type) VALUES (9001, 'Expense Test Tenant 1', 'firm'), (9002, 'Expense Test Tenant 2', 'firm') ON CONFLICT (id) DO NOTHING;
INSERT INTO entities (id, tenant_id, name) VALUES (9001, 9001, 'Expense Test Entity 1'), (9002, 9002, 'Expense Test Entity 2') ON CONFLICT (id) DO NOTHING;

-- Create entity memberships
INSERT INTO entity_memberships (entity_id, user_id, role)
VALUES
(9001, '11111111-1111-1111-1111-111111111111'::uuid, 'owner'),
(9002, '*************-2222-2222-************'::uuid, 'owner')
ON CONFLICT (entity_id, user_id) DO NOTHING;

-- Insert test expense category
INSERT INTO expense_categories (code, name) VALUES ('TEST_CAT', 'Test Category') ON CONFLICT (code) DO NOTHING;

-- Test RLS for expense_category_rules
SELECT set_config('request.jwt.claims', '{"sub": "11111111-1111-1111-1111-111111111111", "role": "authenticated"}', true);

-- User should be able to insert rules for their entity
SELECT lives_ok(
  $$INSERT INTO expense_category_rules (entity_id, code) VALUES (9001, 'TEST_CAT')$$,
  'User should be able to insert expense category rules for their entity'
);

-- Test RLS for posting_classifications
SELECT lives_ok(
  $$INSERT INTO posting_classifications (entity_id, source_kind, source_id, category_code, decided_by) VALUES (9001, 'document', 1, 'TEST_CAT', 'user')$$,
  'User should be able to insert posting classifications for their entity'
);

-- Test RLS for tax_estimates
SELECT lives_ok(
  $$INSERT INTO tax_estimates (entity_id, year, payload_json) VALUES (9001, 2024, '{"test": true}')$$,
  'User should be able to insert tax estimates for their entity'
);

-- Switch to different user and test isolation
SELECT set_config('request.jwt.claims', '{"sub": "*************-2222-2222-************", "role": "authenticated"}', true);

-- Note: RLS policies don't apply to superuser (postgres), so we test policy existence instead
SELECT policy_cmd_is('public', 'expense_category_rules', 'expense_category_rules_access', 'ALL', 'expense_category_rules should have RLS policy');
SELECT policy_cmd_is('public', 'posting_classifications', 'posting_classifications_access', 'ALL', 'posting_classifications should have RLS policy');
SELECT policy_cmd_is('public', 'tax_estimates', 'tax_estimates_access', 'ALL', 'tax_estimates should have RLS policy');

-- =============================================================================
-- INDEX TESTS
-- =============================================================================

-- Test that performance indexes exist
SELECT has_index('expense_category_rules', 'idx_expense_category_rules_entity_code', 'expense_category_rules should have entity_code index');
SELECT has_index('posting_classifications', 'idx_posting_classifications_entity_source', 'posting_classifications should have entity_source index');
SELECT has_index('posting_classifications', 'idx_posting_classifications_category', 'posting_classifications should have category index');
SELECT has_index('tax_estimates', 'idx_tax_estimates_entity_year', 'tax_estimates should have entity_year index');

ROLLBACK;
