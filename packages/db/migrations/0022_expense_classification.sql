-- Migration 0022: Expense Classification System
-- Creates tables for expense categorization, fiscal parameters, and tax estimates

-- =============================================================================
-- 1. EXPENSE CATEGORIES (Global defaults)
-- =============================================================================

CREATE TABLE expense_categories (
  code TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  default_income_deduct_pct NUMERIC(5,2) NOT NULL DEFAULT 100 CHECK (default_income_deduct_pct >= 0 AND default_income_deduct_pct <= 100),
  default_vat_deduct_pct NUMERIC(5,2) NOT NULL DEFAULT 100 CHECK (default_vat_deduct_pct >= 0 AND default_vat_deduct_pct <= 100),
  guidance_md TEXT NOT NULL DEFAULT '',
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Enable RLS on expense_categories (system-wide configuration)
ALTER TABLE expense_categories ENABLE ROW LEVEL SECURITY;

-- RLS policy for expense_categories (readable by all authenticated users)
CREATE POLICY expense_categories_select ON expense_categories
  FOR SELECT TO authenticated
  USING (true);

-- RLS policy for expense_categories (only service role can modify)
CREATE POLICY expense_categories_modify ON expense_categories
  FOR ALL TO service_role
  USING (true);

-- =============================================================================
-- 2. EXPENSE CATEGORY RULES (Per-entity overrides)
-- =============================================================================

CREATE TABLE expense_category_rules (
  entity_id BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
  code TEXT NOT NULL REFERENCES expense_categories(code) ON DELETE CASCADE,
  income_deduct_pct NUMERIC(5,2) CHECK (income_deduct_pct IS NULL OR (income_deduct_pct >= 0 AND income_deduct_pct <= 100)),
  vat_deduct_pct NUMERIC(5,2) CHECK (vat_deduct_pct IS NULL OR (vat_deduct_pct >= 0 AND vat_deduct_pct <= 100)),
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  PRIMARY KEY (entity_id, code)
);

-- Enable RLS on expense_category_rules
ALTER TABLE expense_category_rules ENABLE ROW LEVEL SECURITY;

-- RLS policy for expense_category_rules (entity membership required)
CREATE POLICY expense_category_rules_access ON expense_category_rules
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM entity_memberships em
      WHERE em.entity_id = expense_category_rules.entity_id 
      AND em.user_id = auth.uid()
    )
  );

-- =============================================================================
-- 3. POSTING CLASSIFICATIONS (Applied to documents or journal lines)
-- =============================================================================

CREATE TABLE posting_classifications (
  entity_id BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
  source_kind TEXT NOT NULL CHECK (source_kind IN ('document','journal_line')),
  source_id BIGINT NOT NULL,
  category_code TEXT NOT NULL REFERENCES expense_categories(code) ON DELETE CASCADE,
  confidence NUMERIC(5,2) NOT NULL DEFAULT 100 CHECK (confidence >= 0 AND confidence <= 100),
  decided_by TEXT NOT NULL CHECK (decided_by IN ('ai','user')),
  decided_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  PRIMARY KEY (entity_id, source_kind, source_id)
);

-- Enable RLS on posting_classifications
ALTER TABLE posting_classifications ENABLE ROW LEVEL SECURITY;

-- RLS policy for posting_classifications (entity membership required)
CREATE POLICY posting_classifications_access ON posting_classifications
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM entity_memberships em
      WHERE em.entity_id = posting_classifications.entity_id 
      AND em.user_id = auth.uid()
    )
  );

-- =============================================================================
-- 4. FISCAL PARAMS BE (Versioned parameter sets per year)
-- =============================================================================

CREATE TABLE fiscal_params_be (
  year INT NOT NULL,
  payload_json JSONB NOT NULL,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  PRIMARY KEY (year)
);

-- Enable RLS on fiscal_params_be (system-wide configuration)
ALTER TABLE fiscal_params_be ENABLE ROW LEVEL SECURITY;

-- RLS policy for fiscal_params_be (readable by all authenticated users)
CREATE POLICY fiscal_params_be_select ON fiscal_params_be
  FOR SELECT TO authenticated
  USING (true);

-- RLS policy for fiscal_params_be (only service role can modify)
CREATE POLICY fiscal_params_be_modify ON fiscal_params_be
  FOR ALL TO service_role
  USING (true);

-- =============================================================================
-- 5. TAX ESTIMATES (Materialized results with explainability)
-- =============================================================================

CREATE TABLE tax_estimates (
  entity_id BIGINT NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
  year INT NOT NULL,
  payload_json JSONB NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  PRIMARY KEY (entity_id, year, created_at)
);

-- Enable RLS on tax_estimates
ALTER TABLE tax_estimates ENABLE ROW LEVEL SECURITY;

-- RLS policy for tax_estimates (entity membership required)
CREATE POLICY tax_estimates_access ON tax_estimates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM entity_memberships em
      WHERE em.entity_id = tax_estimates.entity_id 
      AND em.user_id = auth.uid()
    )
  );

-- =============================================================================
-- 6. INDEXES FOR PERFORMANCE
-- =============================================================================

-- Index for expense category rules lookups
CREATE INDEX idx_expense_category_rules_entity_code 
ON expense_category_rules(entity_id, code);

-- Index for posting classifications lookups
CREATE INDEX idx_posting_classifications_entity_source 
ON posting_classifications(entity_id, source_kind, source_id);

-- Index for posting classifications by category
CREATE INDEX idx_posting_classifications_category 
ON posting_classifications(category_code);

-- Index for tax estimates by entity and year
CREATE INDEX idx_tax_estimates_entity_year 
ON tax_estimates(entity_id, year);

-- =============================================================================
-- 7. TRIGGERS FOR UPDATED_AT COLUMNS
-- =============================================================================

-- Triggers for updated_at columns
CREATE TRIGGER expense_categories_updated_at
  BEFORE UPDATE ON expense_categories
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER expense_category_rules_updated_at
  BEFORE UPDATE ON expense_category_rules
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER posting_classifications_updated_at
  BEFORE UPDATE ON posting_classifications
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- 8. COMMENTS FOR DOCUMENTATION
-- =============================================================================

COMMENT ON TABLE expense_categories IS 'Global expense category definitions with default deduction percentages';
COMMENT ON TABLE expense_category_rules IS 'Entity-specific overrides for expense category deduction percentages';
COMMENT ON TABLE posting_classifications IS 'AI or user classifications applied to documents or journal lines';
COMMENT ON TABLE fiscal_params_be IS 'Belgian fiscal parameters by year (tax brackets, surcharges, social formulas)';
COMMENT ON TABLE tax_estimates IS 'Materialized tax calculation results with explainability data';

COMMENT ON COLUMN expense_categories.default_income_deduct_pct IS 'Default percentage deductible from income (0-100)';
COMMENT ON COLUMN expense_categories.default_vat_deduct_pct IS 'Default percentage of VAT deductible (0-100)';
COMMENT ON COLUMN expense_categories.guidance_md IS 'Markdown guidance text for this expense category';

COMMENT ON COLUMN posting_classifications.source_kind IS 'Type of source: document (inbox_documents) or journal_line';
COMMENT ON COLUMN posting_classifications.source_id IS 'ID of the source document or journal line';
COMMENT ON COLUMN posting_classifications.confidence IS 'AI confidence score (0-100) or 100 for user decisions';
COMMENT ON COLUMN posting_classifications.decided_by IS 'Whether classification was made by AI or user';

COMMENT ON COLUMN fiscal_params_be.payload_json IS 'JSON containing tax brackets, surcharges, and social security formulas';
COMMENT ON COLUMN tax_estimates.payload_json IS 'JSON containing calculated tax amounts and explainability data';

-- =============================================================================
-- 9. SEED DATA FOR COMMON BELGIAN EXPENSE CATEGORIES
-- =============================================================================

INSERT INTO expense_categories (code, name, default_income_deduct_pct, default_vat_deduct_pct, guidance_md) VALUES
('OFFICE_RENT', 'Office Rent', 100, 100, 'Rent for business premises is fully deductible'),
('OFFICE_SUPPLIES', 'Office Supplies', 100, 100, 'Stationery, printer ink, and other office materials'),
('PROFESSIONAL_FEES', 'Professional Fees', 100, 100, 'Accountant, lawyer, consultant fees'),
('TRAVEL_BUSINESS', 'Business Travel', 100, 100, 'Travel expenses for business purposes'),
('MEALS_BUSINESS', 'Business Meals', 69, 100, 'Business meals are 69% deductible for income tax'),
('VEHICLE_FUEL', 'Vehicle Fuel', 75, 50, 'Fuel costs with standard deduction rates'),
('VEHICLE_MAINTENANCE', 'Vehicle Maintenance', 100, 100, 'Vehicle repairs and maintenance'),
('INSURANCE_BUSINESS', 'Business Insurance', 100, 100, 'Professional liability, property insurance'),
('TELECOM', 'Telecommunications', 100, 100, 'Phone, internet, mobile subscriptions'),
('MARKETING', 'Marketing & Advertising', 100, 100, 'Promotional materials, advertising costs'),
('TRAINING', 'Training & Education', 100, 100, 'Professional development and training'),
('SOFTWARE_LICENSES', 'Software Licenses', 100, 100, 'Business software subscriptions and licenses'),
('BANK_CHARGES', 'Bank Charges', 100, 100, 'Banking fees and transaction costs'),
('REPRESENTATION', 'Representation Costs', 50, 100, 'Entertainment and representation expenses'),
('DEPRECIATION', 'Depreciation', 100, 0, 'Asset depreciation - no VAT component')
ON CONFLICT (code) DO NOTHING;
