-- Migration: Create security_events table for comprehensive audit logging
-- This table stores all security-related events for compliance and monitoring

-- Create security_events table
CREATE TABLE IF NOT EXISTS security_events (
    id BIGSERIAL PRIMARY KEY,
    event_type TEXT NOT NULL,
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    entity_id INTEGER REFERENCES entities(id) ON DELETE SET NULL,
    resource_id TEXT,
    resource_type TEXT,
    action TEXT,
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id TEXT,
    success BOOLEAN NOT NULL DEFAULT true,
    error_message TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_security_events_event_type ON security_events(event_type);
CREATE INDEX IF NOT EXISTS idx_security_events_severity ON security_events(severity);
CREATE INDEX IF NOT EXISTS idx_security_events_user_id ON security_events(user_id);
CREATE INDEX IF NOT EXISTS idx_security_events_entity_id ON security_events(entity_id);
CREATE INDEX IF NOT EXISTS idx_security_events_created_at ON security_events(created_at);
CREATE INDEX IF NOT EXISTS idx_security_events_success ON security_events(success);
CREATE INDEX IF NOT EXISTS idx_security_events_ip_address ON security_events(ip_address);

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_security_events_user_entity ON security_events(user_id, entity_id);
CREATE INDEX IF NOT EXISTS idx_security_events_type_severity ON security_events(event_type, severity);
CREATE INDEX IF NOT EXISTS idx_security_events_entity_time ON security_events(entity_id, created_at);

-- Create partial index for failed events (more critical)
CREATE INDEX IF NOT EXISTS idx_security_events_failures ON security_events(created_at, severity) 
WHERE success = false;

-- Add RLS policies for security events
ALTER TABLE security_events ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see events related to their entities
CREATE POLICY security_events_user_access ON security_events
    FOR SELECT
    USING (
        -- Allow users to see events for entities they have access to
        entity_id IN (
            SELECT vue.entity_id 
            FROM v_user_entities vue 
            WHERE vue.user_id = auth.uid()
        )
        OR 
        -- Allow users to see their own authentication events
        (user_id = auth.uid() AND entity_id IS NULL)
    );

-- Policy: Only service role can insert security events
CREATE POLICY security_events_service_insert ON security_events
    FOR INSERT
    WITH CHECK (
        -- Only allow service role to insert
        auth.jwt() ->> 'role' = 'service_role'
        OR
        -- Allow authenticated users to insert their own events
        (user_id = auth.uid())
    );

-- Policy: No updates or deletes allowed (audit trail integrity)
CREATE POLICY security_events_no_updates ON security_events
    FOR UPDATE
    USING (false);

CREATE POLICY security_events_no_deletes ON security_events
    FOR DELETE
    USING (false);

-- Create a view for security event summaries
CREATE OR REPLACE VIEW v_security_event_summary AS
SELECT 
    event_type,
    severity,
    COUNT(*) as event_count,
    COUNT(*) FILTER (WHERE success = false) as failure_count,
    COUNT(DISTINCT user_id) as unique_users,
    COUNT(DISTINCT entity_id) as unique_entities,
    COUNT(DISTINCT ip_address) as unique_ips,
    MIN(created_at) as first_occurrence,
    MAX(created_at) as last_occurrence
FROM security_events
GROUP BY event_type, severity
ORDER BY event_count DESC;

-- Create a view for recent security events (last 24 hours)
CREATE OR REPLACE VIEW v_recent_security_events AS
SELECT 
    id,
    event_type,
    severity,
    user_id,
    entity_id,
    resource_type,
    action,
    ip_address,
    success,
    error_message,
    created_at
FROM security_events
WHERE created_at >= NOW() - INTERVAL '24 hours'
ORDER BY created_at DESC;

-- Create a view for failed security events
CREATE OR REPLACE VIEW v_security_failures AS
SELECT 
    id,
    event_type,
    severity,
    user_id,
    entity_id,
    resource_id,
    resource_type,
    action,
    details,
    ip_address,
    user_agent,
    error_message,
    created_at
FROM security_events
WHERE success = false
ORDER BY created_at DESC;

-- Add comments for documentation
COMMENT ON TABLE security_events IS 'Comprehensive audit log for all security-related events';
COMMENT ON COLUMN security_events.event_type IS 'Type of security event (e.g., BANK_IMPORT_STARTED, AUTHENTICATION_FAILED)';
COMMENT ON COLUMN security_events.severity IS 'Event severity level: low, medium, high, critical';
COMMENT ON COLUMN security_events.user_id IS 'User who triggered the event (if applicable)';
COMMENT ON COLUMN security_events.entity_id IS 'Entity context for the event (if applicable)';
COMMENT ON COLUMN security_events.resource_id IS 'ID of the specific resource affected';
COMMENT ON COLUMN security_events.resource_type IS 'Type of resource (e.g., bank_account, document)';
COMMENT ON COLUMN security_events.action IS 'Specific action performed';
COMMENT ON COLUMN security_events.details IS 'Additional event-specific data in JSON format';
COMMENT ON COLUMN security_events.ip_address IS 'IP address of the client';
COMMENT ON COLUMN security_events.user_agent IS 'User agent string of the client';
COMMENT ON COLUMN security_events.session_id IS 'Session identifier (truncated for privacy)';
COMMENT ON COLUMN security_events.success IS 'Whether the event/action was successful';
COMMENT ON COLUMN security_events.error_message IS 'Error message if the event failed';
COMMENT ON COLUMN security_events.created_at IS 'Timestamp when the event occurred';

-- Grant appropriate permissions
GRANT SELECT ON security_events TO authenticated;
GRANT SELECT ON v_security_event_summary TO authenticated;
GRANT SELECT ON v_recent_security_events TO authenticated;
GRANT SELECT ON v_security_failures TO authenticated;

-- Service role needs full access for logging
GRANT ALL ON security_events TO service_role;
