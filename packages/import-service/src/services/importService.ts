import { createHash, randomUUID } from 'crypto'
import {
  parseCoda,
  parseCsv,
  type ParsedBatch,
  type CsvConfig,
} from '@belbooks/domain-bank'
import type { CsvImportConfig } from '@belbooks/types'
import type { NormalizedTx, ImportResult } from '../types/normalized-tx'

type ParsedTransaction = {
  bookingDate: string
  valueDate?: string
  amount: string
  transactionCode?: string
  reference?: string
  structuredRef?: string
  counterparty?: string
  counterpartyIban?: string
  raw: Record<string, unknown>[]
}

// Safe utility functions for unknown data access
function validateAndCastParsedBatch(result: unknown): ParsedBatch {
  if (!result || typeof result !== 'object') {
    throw new Error('Invalid parser result: not an object')
  }

  // Safe property extraction with validation
  const entries = safeArrayAccess<ParsedTransaction>(result, 'entries')
  const warnings = safeArrayAccess<string>(result, 'warnings')
  const currency = safeStringProperty(result, 'currency')
  const accountIban = safeStringProperty(result, 'accountIban')
  const openingBalance = safeStringProperty(result, 'openingBalance')
  const closingBalance = safeStringProperty(result, 'closingBalance')
  const statementDate = safeStringProperty(result, 'statementDate')
  const statementCount = safeNumberProperty(result, 'statementCount')

  return {
    entries,
    warnings,
    currency,
    accountIban,
    openingBalance,
    closingBalance,
    statementDate,
    statementCount,
  }
}

function safeStringAccess(value: unknown, fallback = ''): string {
  return typeof value === 'string' ? value : fallback
}

function safeArrayAccess<T>(obj: unknown, key: string): T[] {
  if (!obj || typeof obj !== 'object') return []
  // eslint-disable-next-line security/detect-object-injection
  const value = (obj as Record<string, unknown>)[key]
  return Array.isArray(value) ? (value as T[]) : []
}

function safeStringProperty(obj: unknown, key: string): string | undefined {
  if (!obj || typeof obj !== 'object') return undefined
  // eslint-disable-next-line security/detect-object-injection
  const value = (obj as Record<string, unknown>)[key]
  return typeof value === 'string' ? value : undefined
}

function safeNumberProperty(obj: unknown, key: string): number | undefined {
  if (!obj || typeof obj !== 'object') return undefined
  // eslint-disable-next-line security/detect-object-injection
  const value = (obj as Record<string, unknown>)[key]
  return typeof value === 'number' ? value : undefined
}

function safeNumberParse(value: unknown, fallback = 0): number {
  if (typeof value === 'string') {
    const parsed = parseFloat(value)
    return isNaN(parsed) ? fallback : parsed
  }
  return typeof value === 'number' && !isNaN(value) ? value : fallback
}

export class ImportService {
  importFile(
    fileContent: string,
    format: 'coda' | 'csv',
    _filename?: string,
    csvConfig?: CsvImportConfig
  ): ImportResult {
    const batchId = randomUUID()

    try {
      // Get the parsed and validated batch based on format
      const validatedBatch: ParsedBatch = this.getParsedBatch(
        fileContent,
        format,
        csvConfig
      )

      // Extract validated data - all properties are guaranteed to be properly typed
      const normalizedTxs = this.normalizeTransactions(
        validatedBatch.entries || [],
        validatedBatch.currency
      )

      // Build result with validated properties
      const result: ImportResult = {
        batchId,
        summary: {
          imported: normalizedTxs.length,
          skipped: 0,
          deduped: 0, // Will be handled at database level
        },
        entries: normalizedTxs,
        warnings: validatedBatch.warnings || [],
        ...(validatedBatch.accountIban
          ? { accountIban: validatedBatch.accountIban }
          : {}),
        ...(validatedBatch.openingBalance
          ? { openingBalance: validatedBatch.openingBalance }
          : {}),
        ...(validatedBatch.closingBalance
          ? { closingBalance: validatedBatch.closingBalance }
          : {}),
      }

      return result
    } catch (error) {
      throw new Error(
        `Failed to import ${format} file: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  private getParsedBatch(
    fileContent: string,
    format: 'coda' | 'csv',
    csvConfig?: CsvImportConfig
  ): ParsedBatch {
    switch (format) {
      case 'coda':
        try {
          const rawResult: unknown = parseCoda(fileContent)
          return validateAndCastParsedBatch(rawResult)
        } catch (error) {
          throw new Error(
            `Failed to parse CoDA file: ${error instanceof Error ? error.message : 'Unknown error'}`
          )
        }
      case 'csv':
        return this.parseCsvBatch(fileContent, csvConfig)
      default:
        throw new Error(`Unsupported format: ${format as string}`)
    }
  }

  private parseCsvBatch(
    fileContent: string,
    csvConfig?: CsvImportConfig
  ): ParsedBatch {
    const effectiveConfig = csvConfig
      ? normalizeCsvConfig(csvConfig)
      : autoDetectCsvConfig(fileContent)

    const { entries } = parseCsv(fileContent, effectiveConfig)
    const parsedEntries: ParsedBatch['entries'] = entries.map(entry => ({
      bookingDate: entry.bookingDate,
      valueDate: entry.valueDate,
      amount: entry.amount,
      reference: entry.reference,
      counterparty: entry.counterparty,
      raw: [entry.raw],
    }))

    return {
      entries: parsedEntries,
      warnings: [],
      statementCount: 1,
    }
  }

  private normalizeTransactions(
    entries: ParsedTransaction[],
    currency?: string
  ): NormalizedTx[] {
    if (!Array.isArray(entries)) {
      throw new Error('Invalid entries array provided')
    }

    return entries.map(entry => {
      if (!entry || typeof entry !== 'object') {
        throw new Error('Invalid transaction entry')
      }

      const bookingDate = safeStringAccess(entry.bookingDate)
      const valueDate = safeStringAccess(entry.valueDate, bookingDate)
      const amount = safeNumberParse(entry.amount)

      if (!bookingDate) {
        throw new Error('Transaction missing required booking date')
      }

      const normalizedTx: NormalizedTx = {
        transaction_date: bookingDate,
        value_date: valueDate || bookingDate,
        amount,
        counterparty_name: safeStringAccess(entry.counterparty),
        description: safeStringAccess(entry.reference),
        reference: safeStringAccess(entry.structuredRef),
        transaction_id: this.generateTransactionId(entry),
        dedupe_hash: this.computeDedupeHash(entry),
        currency: safeStringAccess(currency, 'EUR'),
        structured_ref: safeStringAccess(entry.structuredRef),
        raw_json: { raw: entry.raw || {} },
      }

      return normalizedTx
    })
  }

  private generateTransactionId(entry: ParsedTransaction): string {
    // Create deterministic ID from key fields with safe access
    const keyData = [
      safeStringAccess(entry.bookingDate),
      safeStringAccess(entry.valueDate, safeStringAccess(entry.bookingDate)),
      safeStringAccess(entry.amount),
      safeStringAccess(entry.reference),
      safeStringAccess(entry.counterparty),
    ].join('|')

    return createHash('sha256').update(keyData).digest('hex').substring(0, 16)
  }

  private computeDedupeHash(entry: ParsedTransaction): string {
    // Create hash for deduplication using key identifying fields with safe access
    const dedupeData = [
      safeStringAccess(entry.bookingDate),
      safeStringAccess(entry.valueDate, safeStringAccess(entry.bookingDate)),
      safeStringAccess(entry.amount),
      safeStringAccess(entry.reference),
      safeStringAccess(entry.counterparty),
    ].join('|')

    return createHash('sha256').update(dedupeData).digest('hex')
  }
}

function normalizeCsvConfig(config: CsvImportConfig): CsvConfig {
  return {
    delimiter: config.delimiter ?? ',',
    headers: config.headers !== false,
    map: {
      bookingDate: config.map.bookingDate,
      valueDate: config.map.valueDate,
      amount: config.map.amount,
      reference: config.map.reference ?? config.map.bookingDate,
      counterparty: config.map.counterparty,
    },
  }
}

function autoDetectCsvConfig(fileContent: string): CsvConfig {
  const lines = fileContent.split(/\r?\n/).filter(line => line.trim().length > 0)
  if (lines.length < 2) {
    throw new Error('CSV file must include a header row and at least one data row')
  }

  const headerLine = lines[0]
  if (!headerLine) {
    throw new Error('CSV file header row is empty')
  }
  const delimiter = detectDelimiter(headerLine)
  const headers = splitCsvLine(headerLine, delimiter)

  if (headers.length < 3) {
    throw new Error('Unable to detect CSV header columns. Provide csv_config manually.')
  }

  const normalized = headers.map(header => ({
    original: header,
    normalized: normalizeHeader(header),
  }))

  const findHeader = (...candidates: string[]): string | undefined => {
    return normalized.find(h => candidates.some(candidate => h.normalized.includes(candidate)))
      ?.original
  }

  const bookingDate =
    findHeader('transactiondate', 'bookingdate', 'boekingsdatum', 'date') ?? undefined
  const valueDate =
    findHeader('valuedate', 'valuedatum', 'value') ?? undefined
  const amount =
    findHeader('amount', 'bedrag', 'montant', 'total', 'sum', 'debitcredit') ?? undefined
  const reference =
    findHeader('description', 'omschrijving', 'communication', 'memo', 'details', 'note') ??
    findHeader('narration', 'narrative') ??
    undefined
  const counterparty =
    findHeader('counterparty', 'beneficiary', 'payee', 'tegenpartij', 'customer', 'supplier') ??
    undefined

  if (!bookingDate || !amount || !reference) {
    throw new Error(
      'Unable to auto-detect required CSV columns. Provide csv_config with explicit mappings.'
    )
  }

  return {
    delimiter,
    headers: true,
    map: {
      bookingDate,
      valueDate,
      amount,
      reference,
      counterparty,
    },
  }
}

function detectDelimiter(headerLine: string): string {
  const commaCount = (headerLine.match(/,/g) || []).length
  const semicolonCount = (headerLine.match(/;/g) || []).length
  const tabCount = (headerLine.match(/\t/g) || []).length

  if (semicolonCount > commaCount && semicolonCount >= tabCount) return ';'
  if (tabCount > commaCount && tabCount > semicolonCount) return '\t'
  return ','
}

function splitCsvLine(line: string, delimiter: string): string[] {
  const values: string[] = []
  let current = ''
  let inQuotes = false

  for (let i = 0; i < line.length; i++) {
    const char = line.charAt(i)

    if (char === '"') {
      if (inQuotes && line[i + 1] === '"') {
        current += '"'
        i++
      } else {
        inQuotes = !inQuotes
      }
      continue
    }

    if (char === delimiter && !inQuotes) {
      values.push(current.trim())
      current = ''
    } else {
      current += char
    }
  }

  values.push(current.trim())
  return values
}

function normalizeHeader(header: string): string {
  return header.trim().toLowerCase().replace(/[^a-z0-9]/g, '')
}

export const importService = new ImportService()
