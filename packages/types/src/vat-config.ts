// VAT Configuration Types
// Defines the schema for VAT configuration stored in operating_modes.config

import { z } from 'zod'

/**
 * VAT Default Rates Configuration
 * Belgian VAT rates as configured by the user
 */
export const VATDefaultRatesSchema = z.object({
  standard: z.number().min(0).max(1).default(0.21), // 21% standard rate
  reduced1: z.number().min(0).max(1).default(0.12), // 12% reduced rate
  reduced2: z.number().min(0).max(1).default(0.06), // 6% reduced rate
  zero: z.number().min(0).max(1).default(0.0), // 0% exempt rate
})

export type VATDefaultRates = z.infer<typeof VATDefaultRatesSchema>

/**
 * VAT Filing Frequency
 */
export const VATFilingFrequencySchema = z.enum(['monthly', 'quarterly'])
export type VATFilingFrequency = z.infer<typeof VATFilingFrequencySchema>

/**
 * Complete VAT Configuration Schema
 * This is what gets stored in operating_modes.config.vat
 */
export const VATConfigurationSchema = z.object({
  enabled: z.boolean().default(false),
  vatNumber: z.string().optional(),
  filingFrequency: VATFilingFrequencySchema.default('quarterly'),
  defaultRates: VATDefaultRatesSchema,
  country: z.string().default('BE'), // ISO country code
  configuredAt: z.string().datetime().optional(), // ISO datetime when configured
  configuredBy: z.string().uuid().optional(), // User ID who configured
})

export type VATConfiguration = z.infer<typeof VATConfigurationSchema>

/**
 * Operating Modes Config Schema
 * Extends the existing config to include VAT configuration
 */
export const OperatingModeConfigSchema = z
  .object({
    // Legacy VAT flag for backward compatibility
    VATEnabled: z.boolean().optional(),

    // New structured VAT configuration
    vat: VATConfigurationSchema.optional(),

    // Allow other configuration properties
  })
  .passthrough()

export type OperatingModeConfig = z.infer<typeof OperatingModeConfigSchema>

/**
 * VAT Setup Request Schema
 * Used for API requests to configure VAT
 */
export const VATSetupRequestSchema = z.object({
  enabled: z.boolean(),
  vatNumber: z.string().optional(),
  filingFrequency: VATFilingFrequencySchema,
  defaultRates: VATDefaultRatesSchema,
})

export type VATSetupRequest = z.infer<typeof VATSetupRequestSchema>

/**
 * VAT Setup Response Schema
 * Response from VAT setup API
 */
export const VATSetupResponseSchema = z.object({
  success: z.boolean(),
  configuration: VATConfigurationSchema.optional(),
  error: z.string().optional(),
})

export type VATSetupResponse = z.infer<typeof VATSetupResponseSchema>

/**
 * Helper function to check if VAT is enabled in operating mode config
 */
export function isVATEnabled(config: unknown): boolean {
  try {
    const parsed = OperatingModeConfigSchema.parse(config)

    // Check new structured config first
    if (parsed.vat?.enabled !== undefined) {
      return parsed.vat.enabled
    }

    // Fall back to legacy flag
    return parsed.VATEnabled === true
  } catch {
    return false
  }
}

/**
 * Helper function to extract VAT configuration from operating mode config
 */
export function extractVATConfiguration(
  config: unknown
): VATConfiguration | null {
  try {
    const parsed = OperatingModeConfigSchema.parse(config)

    // Return structured config if available
    if (parsed.vat) {
      return parsed.vat
    }

    // Create minimal config from legacy flag
    if (parsed.VATEnabled === true) {
      return {
        enabled: true,
        filingFrequency: 'quarterly',
        defaultRates: {
          standard: 0.21,
          reduced1: 0.12,
          reduced2: 0.06,
          zero: 0.0,
        },
        country: 'BE',
      }
    }

    return null
  } catch {
    return null
  }
}

/**
 * Helper function to create operating mode config with VAT configuration
 */
export function createOperatingModeConfigWithVAT(
  existingConfig: unknown,
  vatConfig: VATConfiguration
): OperatingModeConfig {
  try {
    const parsed = OperatingModeConfigSchema.parse(existingConfig)

    return {
      ...parsed,
      // Set both new and legacy flags for compatibility
      VATEnabled: vatConfig.enabled,
      vat: vatConfig,
    }
  } catch {
    return {
      VATEnabled: vatConfig.enabled,
      vat: vatConfig,
    }
  }
}

/**
 * Belgian VAT Number Validation
 */
export const BELGIAN_VAT_REGEX = /^BE[0-9]{10}$/

export function validateBelgianVATNumber(vatNumber: string): boolean {
  return BELGIAN_VAT_REGEX.test(vatNumber)
}

/**
 * VAT Number Validation Schema
 */
export const VATNumberSchema = z
  .string()
  .optional()
  .refine(val => !val || validateBelgianVATNumber(val), {
    message: 'VAT number must be in format BE0123456789',
  })
