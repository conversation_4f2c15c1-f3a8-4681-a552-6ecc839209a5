# Security Architecture Documentation

## Overview

This document outlines the secure authentication and authorization architecture implemented for the Ledgerly bank import system. The architecture eliminates service role bypass vulnerabilities and implements comprehensive security controls.

## Security Principles

### 1. **Zero Trust Architecture**
- No implicit trust based on network location or service identity
- Every request must be authenticated and authorized
- Principle of least privilege enforced at all levels

### 2. **Defense in Depth**
- Multiple layers of security controls
- Fail-secure defaults
- Comprehensive audit logging

### 3. **Secure by Design**
- Security controls built into the architecture
- Type-safe interfaces prevent bypass attempts
- Centralized security policies

## Authentication Flow

### Client-Side Security
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Web Client    │───▶│  Next.js API     │───▶│      BFF        │
│                 │    │   Routes         │    │                 │
│ - No internal   │    │ - Server-side    │    │ - JWT validation│
│   keys exposed  │    │   validation     │    │ - User context  │
│ - JWT tokens    │    │ - Entity access  │    │ - RLS enforcement│
│   only          │    │   checks         │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Authentication Layers

1. **Client Authentication**
   - JWT tokens from Supabase Auth
   - No internal keys exposed to client
   - Secure HTTP-only cookies for session management

2. **API Route Validation**
   - Server-side JWT verification
   - Entity access validation
   - Request sanitization and validation

3. **BFF Authentication**
   - Comprehensive JWT validation
   - Rate limiting for failed attempts
   - User existence verification
   - Token expiration and audience checks

## Authorization Model

### Entity-Level Access Control

```typescript
interface EntityAccess {
  entityId: number
  userRole: 'owner' | 'admin' | 'accountant' | 'bookkeeper' | 'viewer'
  permissions: string[]
}
```

### Role-Based Permissions

| Role | Bank Import | View Transactions | Modify Transactions | Admin Functions |
|------|-------------|-------------------|---------------------|-----------------|
| Owner | ✅ | ✅ | ✅ | ✅ |
| Admin | ✅ | ✅ | ✅ | ❌ |
| Accountant | ✅ | ✅ | ✅ | ❌ |
| Bookkeeper | ✅ | ✅ | ❌ | ❌ |
| Viewer | ❌ | ✅ | ❌ | ❌ |

### Row Level Security (RLS)

All database operations enforce RLS policies:

```sql
-- Example: Bank transactions access policy
CREATE POLICY bank_transactions_user_access ON bank_transactions
    FOR ALL
    USING (
        entity_id IN (
            SELECT vue.entity_id 
            FROM v_user_entities vue 
            WHERE vue.user_id = auth.uid()
        )
    );
```

## Secure Communication Patterns

### 1. **Client to API Routes**
```typescript
// ✅ SECURE: Using authenticated API routes
const response = await fetch(`/api/entities/${entityId}/bank-transactions/import`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${userToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(importData)
})
```

### 2. **API Routes to BFF**
```typescript
// ✅ SECURE: Server-side internal key usage
const response = await fetch(`${BFF_URL}/entities/${entityId}/bank-import`, {
  method: 'POST',
  headers: {
    'X-Internal-Key': process.env.BFF_INTERNAL_KEY, // Server-side only
    'Authorization': `Bearer ${userToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(validatedData)
})
```

### 3. **BFF to Database**
```typescript
// ✅ SECURE: User-scoped client with RLS
const { data, error } = await userClient
  .from('bank_transactions')
  .select('*')
  .eq('entity_id', entityId) // RLS automatically enforces access
```

## Security Controls

### 1. **Input Validation**
- Zod schema validation for all inputs
- Type-safe parameter parsing
- SQL injection prevention through parameterized queries

### 2. **Rate Limiting**
- IP-based rate limiting for authentication failures
- Progressive lockout (15 minutes after 5 failed attempts)
- Suspicious activity detection and logging

### 3. **Audit Logging**
- Comprehensive security event logging
- Structured audit trail for compliance
- Real-time monitoring and alerting capabilities

### 4. **Error Handling**
- Secure error messages (no sensitive data exposure)
- Consistent error response format
- Detailed logging for debugging (server-side only)

## Security Testing

### Automated Security Tests
1. **Authentication Tests**
   - Invalid token rejection
   - Expired token handling
   - Missing authentication detection

2. **Authorization Tests**
   - Entity access validation
   - Role-based permission enforcement
   - Cross-entity access prevention

3. **Input Validation Tests**
   - Malformed request handling
   - SQL injection prevention
   - XSS prevention

### Manual Security Testing
1. **Penetration Testing**
   - Authentication bypass attempts
   - Authorization escalation testing
   - Input fuzzing and boundary testing

2. **Code Review**
   - Security-focused code reviews
   - Dependency vulnerability scanning
   - Configuration security validation

## Compliance and Monitoring

### Audit Requirements
- All security events logged with timestamps
- User action traceability
- Data access logging
- Configuration change tracking

### Monitoring and Alerting
- Failed authentication monitoring
- Suspicious activity detection
- Performance anomaly detection
- Security event correlation

## Security Incident Response

### Detection
- Automated monitoring alerts
- Audit log analysis
- User behavior analytics

### Response
- Immediate threat containment
- Forensic data collection
- User notification procedures
- System recovery protocols

## Best Practices

### Development
1. **Never expose internal keys to client-side code**
2. **Always validate user permissions before operations**
3. **Use type-safe interfaces for security-critical code**
4. **Implement comprehensive audit logging**
5. **Follow principle of least privilege**

### Deployment
1. **Use environment-specific configurations**
2. **Implement proper secret management**
3. **Enable comprehensive monitoring**
4. **Regular security updates and patches**
5. **Backup and recovery procedures**

### Operations
1. **Regular security assessments**
2. **Incident response drills**
3. **Security awareness training**
4. **Compliance audits**
5. **Continuous improvement processes**

## Migration from Legacy System

### Security Improvements
- ❌ **Before**: Service role bypass with client-side internal keys
- ✅ **After**: User-scoped RLS with server-side authentication

- ❌ **Before**: Minimal JWT validation
- ✅ **After**: Comprehensive token validation with rate limiting

- ❌ **Before**: Basic error logging
- ✅ **After**: Comprehensive audit trail with security monitoring

### Migration Checklist
- [x] Remove client-side internal key exposure
- [x] Implement proper JWT validation
- [x] Replace service role with user-scoped RLS
- [x] Add entity-level authorization middleware
- [x] Implement comprehensive audit logging
- [x] Update RPC functions for user context
- [x] Document secure communication patterns
- [ ] Add comprehensive security testing
- [ ] Update security guidelines and training

## Conclusion

The new security architecture provides robust protection against common attack vectors while maintaining system performance and usability. The implementation follows industry best practices and provides a solid foundation for future security enhancements.
