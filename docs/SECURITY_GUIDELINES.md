# Security Guidelines for Ledgerly Development

## Overview

This document provides comprehensive security guidelines for developing and maintaining the Ledgerly application. These guidelines are based on the security architecture implemented for the bank import system and should be followed for all future development.

## Core Security Principles

### 1. **Zero Trust Architecture**
- Never trust, always verify
- Authenticate and authorize every request
- Implement defense in depth
- Use principle of least privilege

### 2. **Secure by Design**
- Security controls built into architecture
- Type-safe interfaces prevent bypass
- Fail-secure defaults
- Comprehensive audit logging

### 3. **Data Protection**
- Encrypt data in transit and at rest
- Implement proper access controls
- Minimize data exposure
- Regular security assessments

## Authentication Guidelines

### ✅ **DO: Proper JWT Validation**
```typescript
// Comprehensive JWT validation
const { data: { user }, error } = await userClient.auth.getUser(token)
if (error || !user) {
  throw new Error('Invalid token')
}

// Validate token payload
const payload = parseJWTPayload(token)
if (!payload.exp || payload.exp < Date.now() / 1000) {
  throw new Error('Token expired')
}

if (!payload.aud?.includes('authenticated')) {
  throw new Error('Invalid audience')
}
```

### ❌ **DON'T: Client-Side Internal Keys**
```typescript
// NEVER do this - exposes internal keys
const INTERNAL_KEY = process.env.NEXT_PUBLIC_BFF_INTERNAL_KEY

// Instead, use server-side only
const INTERNAL_KEY = process.env.BFF_INTERNAL_KEY // Server-side only
```

### ✅ **DO: Rate Limiting**
```typescript
// Implement rate limiting for authentication failures
const failedAttempts = new Map<string, { count: number; lastAttempt: number }>()
const MAX_FAILED_ATTEMPTS = 5
const LOCKOUT_DURATION = 15 * 60 * 1000 // 15 minutes

function isRateLimited(ip: string): boolean {
  const attempts = failedAttempts.get(ip)
  if (!attempts) return false
  
  return attempts.count >= MAX_FAILED_ATTEMPTS && 
         Date.now() - attempts.lastAttempt < LOCKOUT_DURATION
}
```

## Authorization Guidelines

### ✅ **DO: Entity-Level Access Control**
```typescript
// Always validate entity access
const { data: entityAccess, error } = await userClient
  .from('v_user_entities')
  .select('entity_id, role')
  .eq('entity_id', entityId)
  .in('role', ['owner', 'admin', 'accountant', 'bookkeeper'])
  .single()

if (error || !entityAccess) {
  throw new Error('Access denied')
}
```

### ✅ **DO: Role-Based Permissions**
```typescript
// Define clear role hierarchies
const ROLE_PERMISSIONS = {
  owner: ['read', 'write', 'admin', 'delete'],
  admin: ['read', 'write', 'admin'],
  accountant: ['read', 'write'],
  bookkeeper: ['read', 'write'],
  viewer: ['read']
}

function hasPermission(userRole: string, requiredPermission: string): boolean {
  return ROLE_PERMISSIONS[userRole]?.includes(requiredPermission) ?? false
}
```

### ❌ **DON'T: Service Role Bypass**
```typescript
// NEVER bypass RLS with service role for user operations
const serviceClient = createClient(url, serviceKey) // Dangerous!
const data = await serviceClient.from('sensitive_table').select('*')

// Instead, use user-scoped clients
const userClient = createClient(url, anonKey, { 
  global: { headers: { Authorization: `Bearer ${userToken}` } }
})
const data = await userClient.from('sensitive_table').select('*') // RLS enforced
```

## Input Validation Guidelines

### ✅ **DO: Schema Validation**
```typescript
import { z } from 'zod'

const BankImportSchema = z.object({
  bank_account_id: z.number().positive(),
  format: z.enum(['coda', 'csv', 'qif']),
  content: z.string().min(1),
  filename: z.string().min(1)
})

// Validate all inputs
const validatedData = BankImportSchema.parse(request.body)
```

### ✅ **DO: Sanitize Inputs**
```typescript
// Sanitize file content
function sanitizeFileContent(content: string): string {
  return content
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/[<>]/g, '')
    .trim()
}
```

### ❌ **DON'T: Trust User Input**
```typescript
// NEVER directly use user input in queries
const query = `SELECT * FROM users WHERE id = ${userId}` // SQL injection risk!

// Use parameterized queries
const { data } = await client
  .from('users')
  .select('*')
  .eq('id', userId) // Safe parameterized query
```

## Database Security Guidelines

### ✅ **DO: Enable Row Level Security (RLS)**
```sql
-- Enable RLS on all sensitive tables
ALTER TABLE bank_transactions ENABLE ROW LEVEL SECURITY;

-- Create restrictive policies
CREATE POLICY bank_transactions_user_access ON bank_transactions
    FOR ALL
    USING (
        entity_id IN (
            SELECT vue.entity_id 
            FROM v_user_entities vue 
            WHERE vue.user_id = auth.uid()
        )
    );
```

### ✅ **DO: Use Views for Complex Access Control**
```sql
-- Create secure views for user access
CREATE VIEW v_user_entities AS
SELECT 
    ue.entity_id,
    ue.user_id,
    ue.role,
    e.name as entity_name
FROM user_entities ue
JOIN entities e ON e.id = ue.entity_id
WHERE ue.user_id = auth.uid();
```

### ❌ **DON'T: Disable RLS**
```sql
-- NEVER disable RLS on production tables
ALTER TABLE sensitive_table DISABLE ROW LEVEL SECURITY; -- Dangerous!
```

## API Security Guidelines

### ✅ **DO: Secure API Routes**
```typescript
// Next.js API route with proper authentication
export async function POST(request: Request) {
  // Validate authentication
  const token = request.headers.get('authorization')?.replace('Bearer ', '')
  if (!token) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  // Validate entity access
  const supabase = createClient(url, anonKey, {
    global: { headers: { Authorization: `Bearer ${token}` } }
  })
  
  const { data: entityAccess } = await supabase
    .from('v_user_entities')
    .select('entity_id, role')
    .eq('entity_id', entityId)
    .single()

  if (!entityAccess) {
    return NextResponse.json({ error: 'Access denied' }, { status: 403 })
  }

  // Process request...
}
```

### ✅ **DO: Implement CORS Properly**
```typescript
// Configure CORS for production
const corsOptions = {
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://yourdomain.com'] 
    : ['http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization']
}
```

## Audit Logging Guidelines

### ✅ **DO: Comprehensive Audit Logging**
```typescript
// Log all security-relevant events
await auditLogger.logBankImportStarted(bankAccountId, filename, format)
await auditLogger.logEntityAccessGranted(entityId, userRole, 'bank_import')
await auditLogger.logAuthenticationSuccess('JWT')
```

### ✅ **DO: Structured Logging**
```typescript
// Use structured logging with consistent format
const auditEvent = {
  eventType: 'BANK_IMPORT_STARTED',
  severity: 'medium',
  userId: user.id,
  entityId: entityId,
  resourceId: bankAccountId.toString(),
  resourceType: 'bank_account',
  ipAddress: request.ip,
  userAgent: request.headers['user-agent'],
  timestamp: new Date().toISOString()
}
```

## Error Handling Guidelines

### ✅ **DO: Secure Error Messages**
```typescript
// Return generic error messages to users
try {
  // Sensitive operation
} catch (error) {
  // Log detailed error server-side
  logger.error({ error, userId, entityId }, 'Bank import failed')
  
  // Return generic message to client
  return reply.status(500).send({
    success: false,
    error: 'An error occurred processing your request'
  })
}
```

### ❌ **DON'T: Expose Internal Details**
```typescript
// NEVER expose internal errors to clients
catch (error) {
  return reply.status(500).send({
    success: false,
    error: error.message, // May contain sensitive info!
    stack: error.stack    // Definitely contains sensitive info!
  })
}
```

## Testing Guidelines

### ✅ **DO: Security-Focused Testing**
```typescript
describe('Security Tests', () => {
  it('should reject unauthenticated requests', async () => {
    const response = await request(app)
      .post('/api/sensitive-endpoint')
      .send({ data: 'test' })
    
    expect(response.status).toBe(401)
  })

  it('should prevent cross-entity access', async () => {
    const response = await request(app)
      .get('/api/entities/999/data') // Entity user doesn't own
      .set('Authorization', `Bearer ${userToken}`)
    
    expect(response.status).toBe(403)
  })
})
```

### ✅ **DO: Test Attack Scenarios**
```typescript
// Test common attack vectors
it('should prevent SQL injection', async () => {
  const maliciousInput = "'; DROP TABLE users; --"
  const response = await request(app)
    .post('/api/search')
    .send({ query: maliciousInput })
  
  expect(response.status).not.toBe(500)
})

it('should prevent XSS attacks', async () => {
  const xssPayload = '<script>alert("xss")</script>'
  const response = await request(app)
    .post('/api/comments')
    .send({ content: xssPayload })
  
  expect(response.body.content).not.toContain('<script>')
})
```

## Deployment Security Guidelines

### ✅ **DO: Environment Security**
```bash
# Use proper environment variable management
# Production secrets should never be in code
DATABASE_URL=postgresql://...
SUPABASE_SERVICE_ROLE_KEY=sbp_...
BFF_INTERNAL_KEY=secure-random-key

# NEVER commit secrets to version control
echo "*.env*" >> .gitignore
```

### ✅ **DO: Security Headers**
```typescript
// Implement security headers
app.use((req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff')
  res.setHeader('X-Frame-Options', 'DENY')
  res.setHeader('X-XSS-Protection', '1; mode=block')
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains')
  next()
})
```

## Monitoring and Incident Response

### ✅ **DO: Security Monitoring**
```typescript
// Monitor for suspicious activity
const suspiciousPatterns = [
  'Multiple failed authentication attempts',
  'Cross-entity access attempts',
  'Unusual data access patterns',
  'High-volume requests from single IP'
]

// Set up alerts for security events
if (failedAttempts > threshold) {
  await alertSecurityTeam({
    type: 'AUTHENTICATION_ANOMALY',
    ip: clientIP,
    attempts: failedAttempts,
    timeWindow: '5 minutes'
  })
}
```

### ✅ **DO: Incident Response Plan**
1. **Detection**: Automated monitoring and alerting
2. **Assessment**: Determine scope and impact
3. **Containment**: Isolate affected systems
4. **Eradication**: Remove threats and vulnerabilities
5. **Recovery**: Restore normal operations
6. **Lessons Learned**: Update security measures

## Security Checklist for New Features

Before deploying any new feature, ensure:

- [ ] **Authentication**: All endpoints require proper authentication
- [ ] **Authorization**: Entity-level access control implemented
- [ ] **Input Validation**: All inputs validated and sanitized
- [ ] **RLS Policies**: Database access properly restricted
- [ ] **Audit Logging**: Security events logged
- [ ] **Error Handling**: No sensitive information exposed
- [ ] **Security Tests**: Comprehensive test coverage
- [ ] **Code Review**: Security-focused review completed
- [ ] **Documentation**: Security implications documented

## Conclusion

Following these security guidelines ensures that the Ledgerly application maintains a strong security posture. Regular security reviews, testing, and updates to these guidelines are essential for ongoing protection against evolving threats.

For questions or clarifications on security practices, consult the security team or refer to the detailed security architecture documentation.

## Security Migration Guide

### Migrating from Legacy Authentication

If you're working with legacy code that uses the old authentication patterns, follow this migration guide:

#### 1. Remove Client-Side Internal Keys
```typescript
// ❌ OLD: Client-side internal key exposure
const INTERNAL_KEY = process.env.NEXT_PUBLIC_BFF_INTERNAL_KEY

// ✅ NEW: Server-side only internal keys
// In API routes only:
const INTERNAL_KEY = process.env.BFF_INTERNAL_KEY
```

#### 2. Update API Calls
```typescript
// ❌ OLD: Direct BFF calls from client
const response = await fetch(`${BFF_URL}/entities/${entityId}/bank-import`, {
  headers: {
    'X-Internal-Key': INTERNAL_KEY, // Exposed!
    'Authorization': `Bearer ${token}`
  }
})

// ✅ NEW: Use secure API routes
const response = await fetch(`/api/entities/${entityId}/bank-transactions/import`, {
  headers: {
    'Authorization': `Bearer ${token}` // Only user token
  }
})
```

#### 3. Update BFF Routes
```typescript
// ❌ OLD: Service role bypass
const serviceClient = fastify.supabase
const data = await serviceClient.from('table').select('*')

// ✅ NEW: User-scoped RLS
const userClient = request.userSupabase
if (!userClient || !request.isUserRequest) {
  return reply.status(401).send({ error: 'Authentication required' })
}
const data = await userClient.from('table').select('*')
```

#### 4. Add Entity Authorization
```typescript
// ✅ NEW: Add entity authorization middleware
import { getAuthenticatedUserClientWithEntity } from '../middleware/entity-authorization'

const { client, entityId } = await getAuthenticatedUserClientWithEntity(
  request,
  reply,
  { requiredRoles: ['owner', 'admin', 'accountant'] }
)
if (!client) return // Response already sent
```

#### 5. Add Audit Logging
```typescript
// ✅ NEW: Add comprehensive audit logging
import { createAuditLogger } from '../middleware/audit-logging'

const auditLogger = createAuditLogger(request, userClient, entityId)
await auditLogger.logBankImportStarted(bankAccountId, filename, format)
```

### Testing Migration

Run the security validation script to ensure proper migration:

```bash
# Run security validation
npm run security:validate

# Run security tests
npm test -- --testPathPattern=security

# Check for exposed secrets
npm run security:scan
```
